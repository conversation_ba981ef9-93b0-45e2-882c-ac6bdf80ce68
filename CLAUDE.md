# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Bounty AMS** is a fully implemented vulnerability bounty asset management system with a React frontend, FastAPI backend, and Go-based distributed agents. The system combines automated asset discovery with human knowledge management and agent-based vulnerability verification.

### Working Directory Structure
The main project is located in the `bounty-ams/` subdirectory. All development commands should be run from their respective component directories:
- **Backend commands**: Run from `bounty-ams/backend/`
- **Frontend commands**: Run from `bounty-ams/frontend/`
- **Agent commands**: Run from `bounty-ams/agents/go-agent/`
- **Docker commands**: Run from `bounty-ams/docker/`
- **System validation**: Run from `bounty-ams/` (root)

## Development Environment Setup

### Prerequisites
- **Docker & Docker Compose** - Required for running databases and services
- **Node.js 18+** - For frontend development
- **Python 3.11+** - For backend development
- **Go 1.21+** - For agent development

### Quick Start Commands

#### 1. Start Infrastructure Services
```bash
cd docker
docker-compose up -d
```
This starts PostgreSQL, Elasticsearch, and Redis in containers.

#### 2. Start Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt
python main.py
# or
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```
Backend runs on http://localhost:8000

#### 3. Start Frontend (React + Vite)
```bash
cd frontend
npm install
npm run dev
```
Frontend runs on http://localhost:5173

#### 4. Start Agent (Go)
```bash
cd agents/go-agent
go build -o bounty-agent
./bounty-agent
```

### Development Commands

#### Backend
- **Run backend**: `python main.py` or `uvicorn main:app --reload --host 0.0.0.0 --port 8000`
- **Run tests**: `python test_auth.py` (individual test files, not pytest-based)
- **Database operations**:
  - **Initialize database**: `python init_db.py`
  - **Create admin user**: `python create_default_user.py`
  - **Reset database**: `python reset_database.py`
  - **Force reset database**: `python force_reset_database.py`
- **System validation**: `python validate_system.py` or `python final_verification.py`
- **Data management**:
  - **Create test data**: `python create_test_assets.py`
  - **Create realistic assets**: `python create_realistic_assets.py`
  - **Check data integrity**: `python check_data.py`

#### Frontend
- **Start dev server**: `npm run dev` (runs on port 5173)
- **Build for production**: `npm run build`
- **Lint code**: `npm run lint`
- **Preview build**: `npm run preview`
- **Type checking**: TypeScript is compiled via `tsc -b` in build process

#### Agent
- **Build agent**: `go build -o bounty-agent` (from `agents/go-agent/` directory)
- **Run agent**: `./bounty-agent`
- **Run with specific config**: `./bounty-agent -config config-instance-1.yaml`
- **Start multiple agents**: Use different config files (config-instance-1.yaml, config-instance-2.yaml, etc.)

#### Docker Services
- **Start all services**: `docker-compose up -d` (from `docker/` directory)
- **Stop services**: `docker-compose down`
- **View service status**: `docker-compose ps`
- **View logs**: `docker-compose logs [service_name]`

## Architecture Overview

### Three-Tier Architecture

#### 1. Frontend (React + TypeScript)
- **Location**: `frontend/`
- **Tech Stack**: React 18, TypeScript, Vite, Ant Design, ECharts
- **Key Features**: 
  - Chinese UI with Ant Design components
  - Real-time dashboards with ECharts
  - Asset management and search
  - Agent and task monitoring

#### 2. Backend (FastAPI + Python)
- **Location**: `backend/`
- **Tech Stack**: FastAPI, SQLAlchemy, PostgreSQL, Elasticsearch, Redis
- **Key Features**:
  - RESTful API with JWT authentication
  - Dual database architecture (PostgreSQL + Elasticsearch)
  - Dynamic asset type system
  - Agent management and task distribution

#### 3. Agent (Go)
- **Location**: `agents/go-agent/`
- **Tech Stack**: Go 1.21, logrus, yaml
- **Key Features**:
  - Distributed agent architecture
  - Multiple execution capabilities (subdomain, port scan, service detection)
  - Task polling and result submission
  - Heartbeat monitoring

### Database Architecture

#### PostgreSQL (Relational Data)
- **Tables**: users, asset_types, asset_type_fields, vulnerability_kb, agents, tasks
- **Purpose**: User management, asset type definitions, task queue, agent registry
- **Connection**: localhost:5432

#### Elasticsearch (Asset Data & Search)
- **Indices**: assets-* pattern
- **Purpose**: Asset storage, full-text search, aggregations
- **Connection**: localhost:9200

#### Redis (Cache & Message Queue)
- **Purpose**: Caching, session storage, lightweight message queue
- **Connection**: localhost:6379

## Key Components

### Backend Routes (backend/routes/)
- **auth.py** - JWT authentication and user management
- **dynamic_models.py** - Dynamic asset type system
- **asset_explorer.py** - Unified asset search and management API
- **search_analytics.py** - ES query builder, search templates, and data processing API
- **agents.py** - Agent registration and management
- **tasks.py** - Task creation and monitoring
- **workflows.py** - Workflow management
- **discovered_assets.py** - Asset data endpoints

### Frontend Pages (frontend/src/pages/)
- **Dashboard.tsx** - Main dashboard with metrics
- **AssetExplorer.tsx** - Unified asset search and management interface
- **SearchAnalytics.tsx** - ES query builder and search analysis platform
- **Assets.tsx** - Traditional asset management interface
- **Agents.tsx** - Agent monitoring and management
- **Tasks.tsx** - Task creation and monitoring
- **Workflows.tsx** - Workflow management
- **Search.tsx** - Advanced search interface
- **Models.tsx** - Dynamic asset type management

### Agent Components (agents/go-agent/)
- **main.go** - Main agent loop and coordination
- **client/** - HTTP client for API communication
- **executor/** - Task execution engines
- **config/** - Configuration management

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/user` - Current user info

### Search Analytics Platform
- `POST /api/search-analytics/execute` - Execute ES queries with safety checks
- `GET /api/search-analytics/indices` - Get available ES indices
- `GET /api/search-analytics/query-suggestions` - Get query templates and suggestions
- `POST /api/search-analytics/templates` - Save search templates
- `GET /api/search-analytics/templates` - Get saved search templates
- `DELETE /api/search-analytics/templates/{id}` - Delete search template
- `POST /api/search-analytics/clean` - Clean and process search results
- `POST /api/search-analytics/export` - Export data in various formats

### Asset Management
- `GET /api/asset-explorer/search` - Search assets across multiple ES indices
- `GET /api/asset-explorer/statistics` - Get asset statistics and aggregations
- `POST /api/asset-explorer/ingest` - Add new assets
- `GET /api/asset-explorer/{id}` - Get asset details
- `GET /api/assets/search` - Traditional asset search (legacy)
- `POST /api/assets` - Create asset (legacy)
- `PUT /api/assets/{id}` - Update asset (legacy)

### Agent Management
- `GET /api/agents` - List agents
- `POST /api/agents/register` - Register agent
- `POST /api/agents/heartbeat` - Agent heartbeat

### Task Management
- `GET /api/tasks` - List tasks
- `POST /api/tasks` - Create task
- `PUT /api/tasks/{id}/result` - Submit result

## Default Credentials
- **Username**: admin
- **Password**: password

## Development Workflow

### Making Changes

1. **Frontend Changes**:
   - Edit files in `frontend/src/`
   - Vite provides hot reload automatically
   - Use `npm run lint` to check code quality

2. **Backend Changes**:
   - Edit files in `backend/`
   - FastAPI auto-reloads with `--reload` flag
   - Test endpoints at http://localhost:8000/docs

3. **Agent Changes**:
   - Edit files in `agents/go-agent/`
   - Rebuild with `go build -o bounty-agent`
   - Restart agent to test changes

### Testing

#### Backend Testing
- **Test files**: `backend/test_*.py` (individual test files, not pytest-based)
- **Run specific tests**: `python test_auth.py` (from backend directory)
- **Available test files**:
  - `test_auth.py` - Authentication endpoints
  - `test_elasticsearch.py` - Elasticsearch integration
  - `test_dynamic_models.py` - Dynamic asset types
  - `test_agent_system.py` - Agent system tests
  - `test_asset_search.py` - Asset search functionality
  - `test_api.py` - General API testing
  - `test_asset_aggregation.py` - Asset aggregation features
  - `test_enhanced_assets.py` - Enhanced asset features
- **Important**: Tests are standalone Python scripts, not pytest tests. Run them individually.
- **API documentation**: http://localhost:8000/docs (FastAPI auto-generated docs)
- **System validation**: `python validate_system.py` or `python final_verification.py`

#### Frontend Testing
- Manual testing through browser
- Check browser console for errors
- Test responsive design

#### Agent Testing
- Monitor agent logs for errors
- Check agent status in frontend
- Test task execution

## Key Features

### Dynamic Asset Type System
- Assets types are dynamically configurable
- Custom fields can be added through the Models page
- Field validation and search configuration

### Agent-Based Architecture
- Go agents register with the platform
- Heartbeat system for monitoring
- Task distribution and result collection

### Dual Database Strategy
- PostgreSQL for structured relational data
- Elasticsearch for asset search and analytics
- Redis for caching and messaging

### Search & Analytics
- Full-text search across assets
- Aggregation queries for dashboards
- Custom search filters and sorting

## Configuration

### Environment Variables
- `BOUNTY_USERNAME` - Agent login username
- `BOUNTY_PASSWORD` - Agent login password

### Configuration Files
- `backend/config.py` - Backend configuration with database, Redis, and Elasticsearch settings
- `agents/go-agent/config.yaml` - Default agent configuration
- `agents/go-agent/config-instance-*.yaml` - Multiple agent instance configurations
- `docker/docker-compose.yml` - Service configuration for PostgreSQL, Elasticsearch, Redis
- `frontend/vite.config.ts` - Vite configuration with API proxy setup

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure Docker services are running
2. **Agent Registration**: Check network connectivity and credentials
3. **CORS Issues**: Backend CORS is configured for development
4. **Port Conflicts**: Default ports are 8000 (backend), 5173 (frontend)

### Health Checks
- **Backend health**: `GET /health` - Should return `{"status": "healthy"}`
- **Frontend**: Access http://localhost:5173 (React development server)
- **Database services**: `docker-compose ps` to check PostgreSQL, Elasticsearch, Redis status
- **Service URLs**:
  - PostgreSQL: localhost:5432 (bounty_ams database)
  - Elasticsearch: localhost:9200
  - Redis: localhost:6379

## Production Considerations

### Security
- Change default credentials in production
- Use environment variables for sensitive data
- Enable proper CORS configuration
- Use HTTPS in production

### Performance
- Scale agents horizontally as needed
- Configure Elasticsearch cluster for high availability
- Use Redis clustering for session management
- Enable database connection pooling

### Monitoring
- Agent heartbeat monitoring
- Task execution tracking
- Database performance metrics
- Application logs and error tracking

## System Validation

### Validation Scripts (run from bounty-ams/ root)
- **Complete system validation**: `python validate_system.py`
- **Final verification**: `python final_verification.py`
- **System diagnosis**: `python system_diagnosis.py`
- **Connection testing**: `python connection_test.py`
- **API completeness test**: `python api_completeness_test.py`

### Go Agent Dependencies
- **Go version**: 1.21+
- **Key dependencies**:
  - `github.com/sirupsen/logrus` - Structured logging
  - `gopkg.in/yaml.v2` - Configuration parsing
- **Agent instances**: Multiple agents can run with different config files (config-instance-1.yaml, config-instance-2.yaml, etc.)