import React, { useState } from 'react';
import { Outlet, Navigate, useLocation, useNavigate } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Button,
  theme,
  Typography,
  Badge,
} from 'antd';
import {
  DashboardOutlined,
  DatabaseOutlined,
  RobotOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  BellOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../context/AuthContext';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const { user, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // 完整菜单项
  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/platform-project',
      icon: <DatabaseOutlined />,
      label: '平台项目管理',
    },
    {
      key: 'assets-management',
      icon: <DatabaseOutlined />,
      label: '资产管理',
      children: [
        {
          key: '/unified-assets',
          icon: <DatabaseOutlined />,
          label: '统一资产管理',
        },
        {
          key: '/asset-explorer',
          icon: <SecurityScanOutlined />,
          label: '资产探索器',
        },
        {
          key: '/assets',
          icon: <DatabaseOutlined />,
          label: '资产管理 (传统)',
        },
      ]
    },
    {
      key: '/data-import',
      icon: <FileTextOutlined />,
      label: '数据导入',
    },
    {
      key: 'search-analysis',
      icon: <SecurityScanOutlined />,
      label: '搜索分析',
      children: [
        {
          key: '/search-analytics',
          icon: <SecurityScanOutlined />,
          label: '搜索分析平台',
        },
        {
          key: '/search',
          icon: <SecurityScanOutlined />,
          label: '基础搜索',
        },
        {
          key: '/advanced-search',
          icon: <SecurityScanOutlined />,
          label: '高级搜索',
        },
        {
          key: '/security-dashboard',
          icon: <SecurityScanOutlined />,
          label: '安全分析',
        },
        {
          key: '/data-processing',
          icon: <SecurityScanOutlined />,
          label: '数据处理',
        },
        {
          key: '/intelligent-aggregation',
          icon: <SecurityScanOutlined />,
          label: '智能聚合',
        }
      ],
    },
    {
      key: '/workflows',
      icon: <SecurityScanOutlined />,
      label: '工作流',
    },
    {
      key: '/agents',
      icon: <RobotOutlined />,
      label: 'Agent管理',
    },
    {
      key: '/tasks',
      icon: <SecurityScanOutlined />,
      label: '任务管理',
    },
    {
      key: '/models',
      icon: <SettingOutlined />,
      label: '动态模型',
    },
  ];

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      key: 'divider',
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: colorBgContainer,
          borderRight: '1px solid #f0f0f0',
        }}
      >
        <div style={{ 
          height: 64, 
          padding: '16px', 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <SecurityScanOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          {!collapsed && (
            <Text strong style={{ marginLeft: '8px', fontSize: '16px' }}>
              Bounty AMS
            </Text>
          )}
        </div>
        
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>
      
      <Layout>
        <Header style={{
          padding: '0 16px',
          background: colorBgContainer,
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="middle">
            <Badge count={3} size="small">
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                size="large"
                style={{ color: '#666' }}
              />
            </Badge>
            
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <Text>{user?.username || '未知用户'}</Text>
                <Badge 
                  color={user?.is_admin ? 'green' : 'blue'} 
                  text={user?.is_admin ? '管理员' : '只读用户'}
                />
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{
          margin: '16px',
          padding: 24,
          background: colorBgContainer,
          borderRadius: borderRadiusLG,
        }}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;