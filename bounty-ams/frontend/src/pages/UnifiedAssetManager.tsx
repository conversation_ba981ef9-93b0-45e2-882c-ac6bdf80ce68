import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Space, Alert, Table, Tag, Statistic, Row, Col, Modal, Form, Input, Select, Tooltip, Progress } from 'antd';
import { RocketOutlined, DatabaseOutlined, SyncOutlined, EyeOutlined, EditOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { Option } = Select;

// 资产管道服务
const assetPipelineService = {
  baseURL: '/api/asset-pipeline',
  
  getAuthHeaders: () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
  }),

  // 触发聚合
  runAggregation: async () => {
    const response = await fetch(`${assetPipelineService.baseURL}/run-aggregation`, {
      method: 'POST',
      headers: assetPipelineService.getAuthHeaders()
    });
    return response.json();
  },

  // 获取聚合状态
  getAggregationStatus: async () => {
    const response = await fetch(`${assetPipelineService.baseURL}/aggregation-status`, {
      headers: assetPipelineService.getAuthHeaders()
    });
    return response.json();
  },

  // 获取统一资产
  getUnifiedAssets: async (params: any = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    
    const response = await fetch(`${assetPipelineService.baseURL}/unified-assets?${searchParams}`, {
      headers: assetPipelineService.getAuthHeaders()
    });
    return response.json();
  },

  // 更新资产
  updateAsset: async (assetId: string, updateData: any) => {
    const response = await fetch(`${assetPipelineService.baseURL}/unified-assets/${assetId}`, {
      method: 'PUT',
      headers: assetPipelineService.getAuthHeaders(),
      body: JSON.stringify(updateData)
    });
    return response.json();
  },

  // 批量更新
  bulkUpdateAssets: async (assetIds: string[], updateData: any) => {
    const response = await fetch(`${assetPipelineService.baseURL}/unified-assets/bulk-update`, {
      method: 'POST',
      headers: assetPipelineService.getAuthHeaders(),
      body: JSON.stringify({ asset_ids: assetIds, update_data: updateData })
    });
    return response.json();
  },

  // 获取资产关系
  getAssetRelationships: async (assetId: string) => {
    const response = await fetch(`${assetPipelineService.baseURL}/asset-relationships/${assetId}`, {
      headers: assetPipelineService.getAuthHeaders()
    });
    return response.json();
  }
};

const UnifiedAssetManager: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [aggregationStatus, setAggregationStatus] = useState<any>(null);
  const [assets, setAssets] = useState<any[]>([]);
  const [pagination, setPagination] = useState({ total: 0, page: 1, size: 50 });
  const [filters, setFilters] = useState<any>({});
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [bulkUpdateModalVisible, setBulkUpdateModalVisible] = useState(false);
  const [relationshipsModalVisible, setRelationshipsModalVisible] = useState(false);
  const [currentAsset, setCurrentAsset] = useState<any>(null);
  const [relatedAssets, setRelatedAssets] = useState<any[]>([]);
  const [statusMessage, setStatusMessage] = useState('');

  const [editForm] = Form.useForm();
  const [bulkForm] = Form.useForm();

  // 加载聚合状态
  const loadAggregationStatus = async () => {
    try {
      const result = await assetPipelineService.getAggregationStatus();
      if (result.success) {
        setAggregationStatus(result.data);
      }
    } catch (error) {
      console.error('加载聚合状态失败:', error);
    }
  };

  // 加载资产列表
  const loadAssets = async (page = 1) => {
    setLoading(true);
    try {
      const params = { ...filters, page, size: pagination.size };
      const result = await assetPipelineService.getUnifiedAssets(params);
      
      if (result.success) {
        setAssets(result.data.assets);
        setPagination(result.data.pagination);
      }
    } catch (error) {
      console.error('加载资产失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 触发聚合
  const runAggregation = async () => {
    setLoading(true);
    try {
      const result = await assetPipelineService.runAggregation();
      if (result.success) {
        setStatusMessage('✅ 资产聚合任务已启动，请稍后查看结果');
        setTimeout(() => {
          loadAggregationStatus();
          loadAssets();
        }, 5000);
      }
    } catch (error) {
      setStatusMessage('❌ 启动聚合任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑资产
  const editAsset = (asset: any) => {
    setCurrentAsset(asset);
    editForm.setFieldsValue({
      status: asset.status,
      confidence: asset.confidence,
      platform_id: asset.platform_id,
      project_id: asset.project_id,
      tags: asset.tags ? asset.tags.join(', ') : ''
    });
    setEditModalVisible(true);
  };

  // 保存编辑
  const saveEdit = async (values: any) => {
    try {
      const updateData = {
        ...values,
        tags: values.tags ? values.tags.split(',').map((t: string) => t.trim()).filter(Boolean) : [],
        verified_at: values.status === 'verified' ? new Date().toISOString() : null
      };
      
      const result = await assetPipelineService.updateAsset(currentAsset.asset_id, updateData);
      if (result.success) {
        setStatusMessage('✅ 资产更新成功');
        setEditModalVisible(false);
        loadAssets(pagination.page);
      }
    } catch (error) {
      setStatusMessage('❌ 资产更新失败');
    }
  };

  // 批量更新
  const saveBulkUpdate = async (values: any) => {
    try {
      const updateData = {
        ...values,
        tags: values.tags ? values.tags.split(',').map((t: string) => t.trim()).filter(Boolean) : undefined
      };
      
      // 移除空值
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined || updateData[key] === '') {
          delete updateData[key];
        }
      });
      
      const result = await assetPipelineService.bulkUpdateAssets(selectedAssets, updateData);
      if (result.success) {
        setStatusMessage(`✅ 成功更新 ${result.updated_count} 个资产`);
        setBulkUpdateModalVisible(false);
        setSelectedAssets([]);
        loadAssets(pagination.page);
      }
    } catch (error) {
      setStatusMessage('❌ 批量更新失败');
    }
  };

  // 查看关联资产
  const viewRelationships = async (asset: any) => {
    try {
      const result = await assetPipelineService.getAssetRelationships(asset.asset_id);
      if (result.success) {
        setCurrentAsset(result.data.main_asset);
        setRelatedAssets(result.data.related_assets);
        setRelationshipsModalVisible(true);
      }
    } catch (error) {
      setStatusMessage('❌ 加载关联资产失败');
    }
  };

  useEffect(() => {
    loadAggregationStatus();
    loadAssets();
  }, []);

  useEffect(() => {
    loadAssets(1);
  }, [filters]);

  // 表格列定义
  const columns: ColumnsType<any> = [
    {
      title: '资产值',
      dataIndex: 'asset_value',
      key: 'asset_value',
      width: 200,
      render: (value: string) => <Text code style={{ fontSize: '11px' }}>{value}</Text>
    },
    {
      title: '类型',
      dataIndex: 'asset_type',
      key: 'asset_type',
      width: 100,
      render: (value: string) => <Tag color="blue">{value}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (value: string) => {
        const colors = {
          discovered: 'default',
          verified: 'green',
          validated: 'blue',
          false_positive: 'red'
        };
        return <Tag color={colors[value as keyof typeof colors] || 'default'}>{value}</Tag>;
      }
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 100,
      render: (value: number) => (
        <div>
          <Progress 
            percent={Math.round(value * 100)} 
            size="small" 
            strokeColor={value > 0.7 ? '#52c41a' : value > 0.4 ? '#faad14' : '#ff4d4f'}
          />
          <Text style={{ fontSize: '10px' }}>{(value * 100).toFixed(0)}%</Text>
        </div>
      )
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 100,
      render: (value: string) => <Tag size="small">{value}</Tag>
    },
    {
      title: '平台',
      dataIndex: 'platform_id',
      key: 'platform_id',
      width: 100,
      render: (value: string) => value ? <Tag color="purple">{value}</Tag> : '-'
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <Space wrap>
          {tags?.slice(0, 2).map((tag: string) => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
          {tags?.length > 2 && <Text type="secondary">+{tags.length - 2}</Text>}
        </Space>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (value: string) => value ? new Date(value).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button size="small" icon={<EditOutlined />} onClick={() => editAsset(record)} />
          </Tooltip>
          <Tooltip title="查看关联">
            <Button size="small" icon={<LinkOutlined />} onClick={() => viewRelationships(record)} />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        统一资产管理
      </Title>
      
      <Alert
        message="统一资产管道"
        description="将多个ES索引的资产数据聚合为统一的资产数据库，支持关联分析和生命周期管理。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {statusMessage && (
        <Alert
          message={statusMessage}
          type={statusMessage.includes('✅') ? 'success' : 'error'}
          style={{ marginBottom: 16 }}
          showIcon
          closable
          onClose={() => setStatusMessage('')}
        />
      )}

      {/* 聚合状态 */}
      {aggregationStatus && (
        <Card title="聚合状态" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={4}>
              <Statistic title="总资产数" value={aggregationStatus.overview.total_assets} />
            </Col>
            <Col span={4}>
              <Statistic title="资产类型" value={aggregationStatus.overview.asset_types} />
            </Col>
            <Col span={4}>
              <Statistic title="平台数量" value={aggregationStatus.overview.platforms} />
            </Col>
            <Col span={4}>
              <Statistic title="项目数量" value={aggregationStatus.overview.projects} />
            </Col>
            <Col span={4}>
              <Statistic title="平均置信度" value={aggregationStatus.overview.avg_confidence} precision={2} />
            </Col>
            <Col span={4}>
              <Button 
                type="primary" 
                icon={<RocketOutlined />} 
                onClick={runAggregation}
                loading={loading}
              >
                运行聚合
              </Button>
            </Col>
          </Row>
        </Card>
      )}

      {/* 过滤器 */}
      <Card title="筛选条件" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Select
              placeholder="资产类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters({ ...filters, asset_type: value })}
            >
              <Option value="domain">域名</Option>
              <Option value="subdomain">子域名</Option>
              <Option value="ip">IP</Option>
              <Option value="port">端口</Option>
              <Option value="url">URL</Option>
              <Option value="service">服务</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters({ ...filters, status: value })}
            >
              <Option value="discovered">已发现</Option>
              <Option value="verified">已验证</Option>
              <Option value="validated">已确认</Option>
              <Option value="false_positive">误报</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="搜索资产值"
              allowClear
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />
          </Col>
          <Col span={4}>
            <Input
              placeholder="最小置信度"
              type="number"
              min={0}
              max={1}
              step={0.1}
              onChange={(e) => setFilters({ ...filters, confidence_min: e.target.value })}
            />
          </Col>
          <Col span={4}>
            <Button icon={<SyncOutlined />} onClick={() => loadAssets(1)}>刷新</Button>
          </Col>
          <Col span={4}>
            {selectedAssets.length > 0 && (
              <Button 
                type="primary"
                onClick={() => setBulkUpdateModalVisible(true)}
              >
                批量更新({selectedAssets.length})
              </Button>
            )}
          </Col>
        </Row>
      </Card>

      {/* 资产表格 */}
      <Card title={`统一资产 (${pagination.total} 条)`}>
        <Table
          columns={columns}
          dataSource={assets}
          rowKey="asset_id"
          loading={loading}
          rowSelection={{
            selectedRowKeys: selectedAssets,
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedAssets(selectedRowKeys as string[]);
            }
          }}
          pagination={{
            current: pagination.page,
            pageSize: pagination.size,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setPagination({ ...pagination, page, size });
              loadAssets(page);
            }
          }}
          scroll={{ x: 1200 }}
          size="small"
        />
      </Card>

      {/* 编辑资产模态框 */}
      <Modal
        title="编辑资产"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form form={editForm} onFinish={saveEdit} layout="vertical">
          <Form.Item name="status" label="状态">
            <Select>
              <Option value="discovered">已发现</Option>
              <Option value="verified">已验证</Option>
              <Option value="validated">已确认</Option>
              <Option value="false_positive">误报</Option>
            </Select>
          </Form.Item>
          <Form.Item name="confidence" label="置信度">
            <Input type="number" min={0} max={1} step={0.1} />
          </Form.Item>
          <Form.Item name="platform_id" label="平台ID">
            <Input />
          </Form.Item>
          <Form.Item name="project_id" label="项目ID">
            <Input />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Input placeholder="用逗号分隔" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">保存</Button>
              <Button onClick={() => setEditModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量更新模态框 */}
      <Modal
        title={`批量更新 ${selectedAssets.length} 个资产`}
        open={bulkUpdateModalVisible}
        onCancel={() => setBulkUpdateModalVisible(false)}
        footer={null}
      >
        <Form form={bulkForm} onFinish={saveBulkUpdate} layout="vertical">
          <Form.Item name="status" label="状态">
            <Select placeholder="选择要更新的状态">
              <Option value="discovered">已发现</Option>
              <Option value="verified">已验证</Option>
              <Option value="validated">已确认</Option>
              <Option value="false_positive">误报</Option>
            </Select>
          </Form.Item>
          <Form.Item name="confidence" label="置信度">
            <Input type="number" min={0} max={1} step={0.1} placeholder="0.0 - 1.0" />
          </Form.Item>
          <Form.Item name="platform_id" label="平台ID">
            <Input placeholder="统一设置平台ID" />
          </Form.Item>
          <Form.Item name="project_id" label="项目ID">
            <Input placeholder="统一设置项目ID" />
          </Form.Item>
          <Form.Item name="tags" label="标签">
            <Input placeholder="用逗号分隔，会覆盖原有标签" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">批量更新</Button>
              <Button onClick={() => setBulkUpdateModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 关联资产模态框 */}
      <Modal
        title="关联资产"
        open={relationshipsModalVisible}
        onCancel={() => setRelationshipsModalVisible(false)}
        footer={null}
        width={800}
      >
        {currentAsset && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card title="主资产" size="small">
              <Text strong>{currentAsset.asset_value}</Text>
              <br />
              <Text type="secondary">类型: {currentAsset.asset_type} | 状态: {currentAsset.status}</Text>
            </Card>
            
            <Card title={`相关资产 (${relatedAssets.length} 个)`} size="small">
              {relatedAssets.length > 0 ? (
                <Table
                  columns={[
                    { title: '资产值', dataIndex: 'asset_value', key: 'asset_value' },
                    { title: '类型', dataIndex: 'asset_type', key: 'asset_type' },
                    { title: '状态', dataIndex: 'status', key: 'status' },
                    { title: '置信度', dataIndex: 'confidence', key: 'confidence', render: (v: number) => (v * 100).toFixed(0) + '%' }
                  ]}
                  dataSource={relatedAssets}
                  rowKey="asset_id"
                  pagination={false}
                  size="small"
                />
              ) : (
                <Alert message="暂无相关资产" type="info" showIcon />
              )}
            </Card>
          </Space>
        )}
      </Modal>
    </div>
  );
};

export default UnifiedAssetManager;