import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  message,
  Modal,
  Form,
  Upload,
  Tabs,
  Divider,
  Badge,
  Tooltip,
  Progress,
  Descriptions,
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  UploadOutlined,
  DatabaseOutlined,
  SecurityScanOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

// 资产探索器服务
const assetExplorerService = {
  baseURL: '/api/asset-explorer',
  
  getAuthHeaders: () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
  }),

  // 搜索资产
  searchAssets: async (params: any) => {
    const queryParams = new URLSearchParams();
    if (params.q) queryParams.append('q', params.q);
    if (params.asset_type) queryParams.append('asset_type', params.asset_type);
    if (params.data_source) queryParams.append('data_source', params.data_source);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.size) queryParams.append('size', params.size.toString());

    const response = await fetch(`${assetExplorerService.baseURL}/search?${queryParams}`, {
      headers: assetExplorerService.getAuthHeaders()
    });
    return response.json();
  },

  // 获取统计信息
  getStatistics: async () => {
    const response = await fetch(`${assetExplorerService.baseURL}/statistics`, {
      headers: assetExplorerService.getAuthHeaders()
    });
    return response.json();
  },

  // 摄取资产
  ingestAssets: async (assets: any[]) => {
    const response = await fetch(`${assetExplorerService.baseURL}/ingest`, {
      method: 'POST',
      headers: assetExplorerService.getAuthHeaders(),
      body: JSON.stringify(assets)
    });
    return response.json();
  },

  // 获取单个资产
  getAsset: async (assetId: string) => {
    const response = await fetch(`${assetExplorerService.baseURL}/${assetId}`, {
      headers: assetExplorerService.getAuthHeaders()
    });
    return response.json();
  }
};

const AssetExplorer: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<any[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [searchParams, setSearchParams] = useState({
    q: '',
    asset_type: '',
    data_source: '',
    page: 1,
    size: 20
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 模态框状态
  const [ingestModalVisible, setIngestModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<any>(null);

  // 表单
  const [ingestForm] = Form.useForm();

  // 资产类型选项
  const assetTypes = [
    { value: 'domain', label: '域名' },
    { value: 'subdomain', label: '子域名' },
    { value: 'ip', label: 'IP地址' },
    { value: 'url', label: 'URL' },
    { value: 'port', label: '端口' },
    { value: 'email', label: '邮箱' },
    { value: 'certificate', label: '证书' },
    { value: 'service', label: '服务' },
    { value: 'other', label: '其他' }
  ];

  // 数据源选项
  const dataSources = [
    { value: 'agent_scan', label: 'Agent扫描' },
    { value: 'manual_import', label: '手动导入' },
    { value: 'api_fetch', label: 'API获取' },
    { value: 'bulk_upload', label: '批量上传' }
  ];

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 并行加载搜索结果和统计信息
      const [searchResult, statsResult] = await Promise.all([
        assetExplorerService.searchAssets(searchParams),
        assetExplorerService.getStatistics()
      ]);

      if (searchResult.success) {
        setAssets(searchResult.data.assets);
        setPagination({
          current: searchResult.data.page,
          pageSize: searchResult.data.size,
          total: searchResult.data.total
        });
      }

      if (statsResult.success) {
        setStatistics(statsResult.data);
      }
    } catch (error) {
      message.error('加载数据失败');
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadData();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    setSearchParams({ ...searchParams, page: 1 });
    loadData();
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({
      q: '',
      asset_type: '',
      data_source: '',
      page: 1,
      size: 20
    });
    setTimeout(loadData, 100);
  };

  // 分页处理
  const handleTableChange = (pagination: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      size: pagination.pageSize
    });
    setTimeout(loadData, 100);
  };

  // 查看详情
  const handleViewDetail = async (asset: any) => {
    try {
      const result = await assetExplorerService.getAsset(asset.id);
      if (result.success) {
        setSelectedAsset(result.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      message.error('获取资产详情失败');
    }
  };

  // 手动摄取资产
  const handleIngestAssets = async (values: any) => {
    try {
      const assets = values.assets.split('\n').filter((line: string) => line.trim()).map((line: string) => {
        const parts = line.trim().split(',');
        return {
          value: parts[0]?.trim(),
          asset_type: values.asset_type,
          name: parts[1]?.trim() || '',
          description: parts[2]?.trim() || '',
          tags: values.tags ? values.tags.split(',').map((t: string) => t.trim()) : []
        };
      });

      const result = await assetExplorerService.ingestAssets(assets);
      if (result.success) {
        message.success(`成功摄取 ${result.data.success_count} 个资产`);
        setIngestModalVisible(false);
        ingestForm.resetFields();
        loadData();
      } else {
        message.error('摄取失败');
      }
    } catch (error) {
      message.error('摄取资产失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<any> = [
    {
      title: '资产值',
      dataIndex: ['source', 'data', 'value'],
      key: 'value',
      width: 200,
      render: (value: string, record: any) => {
        // 适配不同的数据结构
        const displayValue = record.source?.data?.value || value || 'N/A';
        return (
          <Button 
            type="link" 
            onClick={() => handleViewDetail(record)}
            style={{ padding: 0, height: 'auto' }}
          >
            <Text code style={{ fontSize: '12px' }}>{displayValue}</Text>
          </Button>
        );
      },
    },
    {
      title: '资产类型',
      dataIndex: ['source', 'metadata', 'asset_type'],
      key: 'asset_type',
      width: 100,
      render: (type: string, record: any) => {
        const assetType = record.source?.metadata?.asset_type || type;
        const typeConfig = assetTypes.find(t => t.value === assetType);
        return <Tag color="blue">{typeConfig?.label || assetType}</Tag>;
      },
    },
    {
      title: '名称',
      dataIndex: ['source', 'data', 'name'],
      key: 'name',
      width: 150,
      render: (name: string, record: any) => {
        const displayName = record.source?.data?.name || name || record.source?.original?.asset_host || '';
        return displayName || <Text type="secondary">-</Text>;
      },
    },
    {
      title: '数据源',
      dataIndex: ['source', 'metadata', 'data_source'],
      key: 'data_source',
      width: 120,
      render: (source: string, record: any) => {
        const dataSource = record.source?.metadata?.data_source || source;
        const sourceConfig = dataSources.find(s => s.value === dataSource);
        return <Tag color="green">{sourceConfig?.label || dataSource}</Tag>;
      },
    },
    {
      title: '质量等级',
      dataIndex: ['source', 'metadata', 'quality_level'],
      key: 'quality_level',
      width: 100,
      render: (level: string, record: any) => {
        const qualityLevel = record.source?.metadata?.quality_level || level || 'medium';
        const colors = {
          high: 'success',
          medium: 'warning',
          low: 'error',
          unknown: 'default'
        };
        const labels = {
          high: '高',
          medium: '中',
          low: '低',
          unknown: '未知'
        };
        return <Badge status={colors[qualityLevel as keyof typeof colors]} text={labels[qualityLevel as keyof typeof labels]} />;
      },
    },
    {
      title: '标签',
      dataIndex: ['source', 'data', 'tags'],
      key: 'tags',
      width: 150,
      render: (tags: string[], record: any) => {
        const assetTags = record.source?.data?.tags || tags || record.source?.original?.tags || [];
        return (
          <Space wrap>
            {assetTags?.slice(0, 2).map((tag: string, index: number) => (
              <Tag key={index} size="small">{tag}</Tag>
            ))}
            {assetTags?.length > 2 && <Text type="secondary">+{assetTags.length - 2}</Text>}
          </Space>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: ['source', 'metadata', 'created_at'],
      key: 'created_at',
      width: 150,
      render: (date: string, record: any) => {
        const createdAt = record.source?.metadata?.created_at || date || record.source?.original?.discovered_at || record.source?.original?.['@timestamp'];
        return (
          <Text type="secondary">
            {createdAt ? new Date(createdAt).toLocaleString() : '-'}
          </Text>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (record: any) => (
        <Button 
          type="text" 
          size="small"
          icon={<FileTextOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SecurityScanOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
        资产探索器
      </Title>
      
      <Alert
        message="资产探索器"
        description="统一的资产搜索和管理平台，支持多数据源检索、智能搜索和实时统计分析。"
        type="success"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic 
                title="总资产数" 
                value={statistics.total_assets} 
                prefix={<DatabaseOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="资产类型" 
                value={statistics.asset_types?.length || 0} 
                suffix="种"
                prefix={<FilterOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="数据源" 
                value={statistics.data_sources?.length || 0} 
                suffix="个"
                prefix={<UploadOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="高质量资产" 
                value={statistics.quality_levels?.find((q: any) => q.key === 'high')?.count || 0}
                prefix={<Badge status="success" />}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索和操作区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input
              placeholder="搜索资产..."
              value={searchParams.q}
              onChange={(e) => setSearchParams({ ...searchParams, q: e.target.value })}
              onPressEnter={handleSearch}
              prefix={<SearchOutlined />}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="资产类型"
              value={searchParams.asset_type || undefined}
              onChange={(value) => setSearchParams({ ...searchParams, asset_type: value || '' })}
              allowClear
              style={{ width: '100%' }}
            >
              {assetTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="数据源"
              value={searchParams.data_source || undefined}
              onChange={(value) => setSearchParams({ ...searchParams, data_source: value || '' })}
              allowClear
              style={{ width: '100%' }}
            >
              {dataSources.map(source => (
                <Option key={source.value} value={source.value}>{source.label}</Option>
              ))}
            </Select>
          </Col>
          <Col span={10}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setIngestModalVisible(true)}>
                添加资产
              </Button>
              <Button icon={<UploadOutlined />} onClick={() => setUploadModalVisible(true)}>
                批量导入
              </Button>
              <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 资产表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={assets}
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          rowKey="id"
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 手动摄取模态框 */}
      <Modal
        title="手动添加资产"
        open={ingestModalVisible}
        onCancel={() => setIngestModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={ingestForm} onFinish={handleIngestAssets} layout="vertical">
          <Form.Item
            name="asset_type"
            label="资产类型"
            rules={[{ required: true, message: '请选择资产类型' }]}
          >
            <Select placeholder="选择资产类型">
              {assetTypes.map(type => (
                <Option key={type.value} value={type.value}>{type.label}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="assets"
            label="资产列表"
            rules={[{ required: true, message: '请输入资产' }]}
            extra="每行一个资产，格式：资产值,名称,描述（名称和描述可选）"
          >
            <TextArea
              rows={8}
              placeholder={`example.com,示例网站,这是一个示例域名
192.168.1.1,内网网关
https://api.example.com,API接口`}
            />
          </Form.Item>
          
          <Form.Item
            name="tags"
            label="标签"
            extra="多个标签用逗号分隔"
          >
            <Input placeholder="web,api,internal" />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                添加资产
              </Button>
              <Button onClick={() => setIngestModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 资产详情模态框 */}
      <Modal
        title="资产详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedAsset && (
          <div>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="资产ID">{selectedAsset.id}</Descriptions.Item>
              <Descriptions.Item label="资产值">{selectedAsset.source?.data?.value}</Descriptions.Item>
              <Descriptions.Item label="资产类型">
                <Tag color="blue">{selectedAsset.source?.metadata?.asset_type}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数据源">
                <Tag color="green">{selectedAsset.source?.metadata?.data_source}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="质量等级">
                <Badge 
                  status={selectedAsset.source?.metadata?.quality_level === 'high' ? 'success' : 'warning'} 
                  text={selectedAsset.source?.metadata?.quality_level} 
                />
              </Descriptions.Item>
              <Descriptions.Item label="置信度">{selectedAsset.source?.metadata?.confidence}</Descriptions.Item>
            </Descriptions>
            
            <Divider />
            
            <Title level={5}>详细信息</Title>
            <pre style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px', overflow: 'auto' }}>
              {JSON.stringify(selectedAsset.source, null, 2)}
            </pre>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AssetExplorer;
