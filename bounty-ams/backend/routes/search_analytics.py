"""
搜索分析平台 API路由
提供ES查询构建、模板保存、数据清洗和导出功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, insert, update, delete
from sqlalchemy.orm import selectinload
from typing import List, Dict, Any, Optional
import json
import csv
import io
import re
from datetime import datetime, timedelta
import hashlib
from uuid import UUID, uuid4

from database import get_db
from auth import get_current_user
from models_dynamic import User, ModelType, ModelField
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/search-analytics", tags=["search-analytics"])

# 创建搜索模板表（如果不存在）
async def ensure_search_templates_table(db: AsyncSession):
    """确保搜索模板表存在"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS search_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        query_data JSONB NOT NULL,
        tags TEXT[],
        created_by_user_id UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_search_templates_user_id ON search_templates(created_by_user_id);
    CREATE INDEX IF NOT EXISTS idx_search_templates_tags ON search_templates USING GIN(tags);
    """
    await db.execute(text(create_table_sql))
    await db.commit()

# 创建数据清洗规则表
async def ensure_cleaning_rules_table(db: AsyncSession):
    """确保数据清洗规则表存在"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS data_cleaning_rules (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        rule_type VARCHAR(50) NOT NULL,
        source_field VARCHAR(100) NOT NULL,
        target_field VARCHAR(100),
        mapping_config JSONB,
        transformation_rules JSONB,
        conditions JSONB,
        priority INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_by_user_id UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_cleaning_rules_user_id ON data_cleaning_rules(created_by_user_id);
    CREATE INDEX IF NOT EXISTS idx_cleaning_rules_type ON data_cleaning_rules(rule_type);
    """
    await db.execute(text(create_table_sql))
    await db.commit()

# 创建字段映射表
async def ensure_field_mapping_table(db: AsyncSession):
    """确保字段映射表存在"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS field_mappings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        source_index VARCHAR(255) NOT NULL,
        target_model_type_id UUID,
        mapping_rules JSONB NOT NULL,
        created_by_user_id UUID REFERENCES users(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_field_mappings_user_id ON field_mappings(created_by_user_id);
    CREATE INDEX IF NOT EXISTS idx_field_mappings_source ON field_mappings(source_index);
    """
    await db.execute(text(create_table_sql))
    await db.commit()

# 智能数据清洗引擎
class DataCleaningEngine:
    """智能数据清洗引擎"""
    
    @staticmethod
    def field_mapping_transform(data: List[Dict], mapping_rules: Dict[str, str]) -> List[Dict]:
        """字段映射转换"""
        transformed_data = []
        for item in data:
            new_item = item.copy()
            source = item.get('_source', {})
            
            # 创建新的_source结构
            new_source = {}
            
            for target_field, source_path in mapping_rules.items():
                # 支持嵌套字段路径，如 'metadata.asset_type'
                value = source
                for path_part in source_path.split('.'):
                    value = value.get(path_part) if isinstance(value, dict) else None
                    if value is None:
                        break
                
                if value is not None:
                    new_source[target_field] = value
            
            # 保留原有字段（如果没有映射规则）
            for key, value in source.items():
                if key not in [rule.split('.')[-1] for rule in mapping_rules.values()]:
                    new_source[key] = value
                    
            new_item['_source'] = new_source
            transformed_data.append(new_item)
        
        return transformed_data
    
    @staticmethod
    def smart_deduplication(data: List[Dict], config: Dict) -> List[Dict]:
        """智能去重"""
        dedup_field = config.get('field', 'asset_value')
        strategy = config.get('strategy', 'keep_highest_score')  # keep_first, keep_last, keep_highest_score
        similarity_threshold = config.get('similarity_threshold', 0.9)
        
        if strategy == 'keep_highest_score':
            # 按评分去重
            field_groups = {}
            for item in data:
                value = item.get('_source', {}).get(dedup_field)
                if value:
                    if value not in field_groups:
                        field_groups[value] = []
                    field_groups[value].append(item)
            
            result = []
            for value, items in field_groups.items():
                # 保留评分最高的
                best_item = max(items, key=lambda x: x.get('_score', 0))
                result.append(best_item)
            
            return result
        
        elif strategy == 'fuzzy_matching':
            # 模糊匹配去重（适用于域名等）
            import difflib
            result = []
            seen_values = []
            
            for item in data:
                value = str(item.get('_source', {}).get(dedup_field, ''))
                if not value:
                    continue
                    
                # 检查是否与已有值相似
                is_similar = False
                for seen_value in seen_values:
                    similarity = difflib.SequenceMatcher(None, value.lower(), seen_value.lower()).ratio()
                    if similarity > similarity_threshold:
                        is_similar = True
                        break
                
                if not is_similar:
                    seen_values.append(value)
                    result.append(item)
            
            return result
        
        else:
            # 简单去重
            seen = set()
            result = []
            for item in data:
                value = item.get('_source', {}).get(dedup_field)
                if value not in seen:
                    seen.add(value)
                    result.append(item)
                elif strategy == 'keep_last':
                    # 移除之前的，保留最新的
                    result = [r for r in result if r.get('_source', {}).get(dedup_field) != value]
                    result.append(item)
            
            return result
    
    @staticmethod
    def data_enrichment(data: List[Dict], enrichment_rules: List[Dict]) -> List[Dict]:
        """数据增强"""
        enriched_data = []
        
        for item in data:
            new_item = item.copy()
            source = new_item.get('_source', {})
            
            for rule in enrichment_rules:
                rule_type = rule.get('type')
                
                if rule_type == 'geo_location' and 'ip' in source:
                    # IP地理位置增强（示例）
                    ip = source.get('ip')
                    if ip:
                        # 这里可以集成真实的IP地理位置服务
                        source['geo'] = {
                            'country': 'Unknown',
                            'city': 'Unknown',
                            'coordinates': [0, 0]
                        }
                
                elif rule_type == 'domain_info' and any('domain' in k for k in source.keys()):
                    # 域名信息增强
                    for key, value in source.items():
                        if 'domain' in key and isinstance(value, str):
                            parts = value.split('.')
                            if len(parts) >= 2:
                                source[f'{key}_tld'] = parts[-1]
                                source[f'{key}_sld'] = '.'.join(parts[-2:])
                
                elif rule_type == 'confidence_calculation':
                    # 置信度计算
                    score_factors = rule.get('factors', {})
                    calculated_confidence = 0.5  # 基础置信度
                    
                    if source.get('verified', False):
                        calculated_confidence += score_factors.get('verified_bonus', 0.3)
                    if source.get('source') == 'manual':
                        calculated_confidence += score_factors.get('manual_bonus', 0.2)
                    
                    source['calculated_confidence'] = min(calculated_confidence, 1.0)
            
            new_item['_source'] = source
            enriched_data.append(new_item)
        
        return enriched_data
    
    @staticmethod
    def data_validation(data: List[Dict], validation_rules: List[Dict]) -> Dict[str, Any]:
        """数据验证"""
        validation_result = {
            'valid_items': [],
            'invalid_items': [],
            'validation_errors': []
        }
        
        for item in data:
            source = item.get('_source', {})
            item_errors = []
            
            for rule in validation_rules:
                field = rule.get('field')
                rule_type = rule.get('type')
                value = source.get(field)
                
                if rule_type == 'required' and not value:
                    item_errors.append(f"Field '{field}' is required")
                
                elif rule_type == 'regex' and value:
                    pattern = rule.get('pattern')
                    if pattern and not re.match(pattern, str(value)):
                        item_errors.append(f"Field '{field}' does not match pattern {pattern}")
                
                elif rule_type == 'range' and isinstance(value, (int, float)):
                    min_val = rule.get('min')
                    max_val = rule.get('max')
                    if min_val is not None and value < min_val:
                        item_errors.append(f"Field '{field}' below minimum {min_val}")
                    if max_val is not None and value > max_val:
                        item_errors.append(f"Field '{field}' above maximum {max_val}")
            
            if item_errors:
                validation_result['invalid_items'].append({
                    'item': item,
                    'errors': item_errors
                })
                validation_result['validation_errors'].extend(item_errors)
            else:
                validation_result['valid_items'].append(item)
        
        return validation_result
    
    @staticmethod
    def multi_source_correlation(datasets: List[List[Dict]], correlation_rules: List[Dict]) -> List[Dict]:
        """多数据源关联"""
        correlated_data = []
        
        if len(datasets) < 2:
            return datasets[0] if datasets else []
        
        primary_dataset = datasets[0]
        
        for primary_item in primary_dataset:
            primary_source = primary_item.get('_source', {})
            correlated_item = primary_item.copy()
            
            for rule in correlation_rules:
                primary_field = rule.get('primary_field')
                secondary_field = rule.get('secondary_field')
                dataset_index = rule.get('secondary_dataset_index', 1)
                
                if dataset_index < len(datasets):
                    secondary_dataset = datasets[dataset_index]
                    primary_value = primary_source.get(primary_field)
                    
                    if primary_value:
                        # 查找匹配项
                        for secondary_item in secondary_dataset:
                            secondary_source = secondary_item.get('_source', {})
                            secondary_value = secondary_source.get(secondary_field)
                            
                            if primary_value == secondary_value:
                                # 合并数据
                                merge_prefix = rule.get('merge_prefix', 'related_')
                                for key, value in secondary_source.items():
                                    correlated_item['_source'][f'{merge_prefix}{key}'] = value
                                break
            
            correlated_data.append(correlated_item)
        
        return correlated_data

# 搜索模板存储 (简单实现，实际应用中应使用数据库)
SEARCH_TEMPLATES = []

@router.post("/execute")
async def execute_es_query(
    query_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    es_client = Depends(get_es_client)
):
    """执行ES查询"""
    try:
        index = query_request.get('index', 'enhanced_asset-2025-07')
        body = query_request.get('body', {'query': {'match_all': {}}})
        
        # 安全检查 - 限制某些危险操作
        if 'delete' in json.dumps(body).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不允许删除操作"
            )
        
        # 限制返回数量，防止查询过大
        if 'size' in body and body['size'] > 1000:
            body['size'] = 1000
        
        start_time = datetime.now()
        
        # 执行查询
        response = await es_client.search(
            index=index,
            body=body,
            ignore_unavailable=True
        )
        
        end_time = datetime.now()
        query_time = (end_time - start_time).total_seconds() * 1000
        
        # 处理结果
        hits = response.get('hits', {}).get('hits', [])
        total = response.get('hits', {}).get('total', {}).get('value', 0)
        aggregations = response.get('aggregations', {})
        
        stats = {
            'took': response.get('took', 0),
            'total': total,
            'max_score': response.get('hits', {}).get('max_score'),
            'shards': response.get('_shards', {}),
            'query_time': query_time
        }
        
        return {
            "success": True,
            "data": {
                "hits": hits,
                "total": total,
                "aggregations": aggregations,
                "stats": stats
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询执行失败: {str(e)}"
        )

@router.post("/templates")
async def save_search_template(
    template: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """保存搜索模板到数据库"""
    try:
        await ensure_search_templates_table(db)
        
        insert_query = text("""
            INSERT INTO search_templates (name, description, query_data, tags, created_by_user_id)
            VALUES (:name, :description, :query_data, :tags, :user_id)
            RETURNING id
        """)
        
        result = await db.execute(insert_query, {
            "name": template['name'],
            "description": template.get('description', ''),
            "query_data": json.dumps(template['query']),
            "tags": template.get('tags', []),
            "user_id": current_user.id
        })
        
        template_id = result.scalar_one()
        await db.commit()
        
        return {
            "success": True,
            "data": {
                "template_id": str(template_id),
                "message": "搜索模板保存成功"
            }
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存搜索模板失败: {str(e)}"
        )

@router.get("/templates")
async def get_search_templates(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """从数据库获取搜索模板"""
    try:
        await ensure_search_templates_table(db)
        
        query = text("""
            SELECT id, name, description, query_data, tags, created_at, updated_at
            FROM search_templates 
            WHERE created_by_user_id = :user_id
            ORDER BY updated_at DESC
        """)
        
        result = await db.execute(query, {"user_id": current_user.id})
        templates = []
        
        for row in result:
            templates.append({
                "id": str(row.id),
                "name": row.name,
                "description": row.description,
                "query": json.loads(row.query_data),
                "tags": row.tags or [],
                "created_at": row.created_at.isoformat(),
                "updated_at": row.updated_at.isoformat() if row.updated_at else None
            })
        
        return {
            "success": True,
            "data": templates
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取搜索模板失败: {str(e)}"
        )

@router.delete("/templates/{template_id}")
async def delete_search_template(
    template_id: str,
    current_user: User = Depends(get_current_user)
):
    """删除搜索模板"""
    try:
        global SEARCH_TEMPLATES
        
        # 找到并删除模板
        original_count = len(SEARCH_TEMPLATES)
        SEARCH_TEMPLATES = [
            template for template in SEARCH_TEMPLATES 
            if not (template.get('id') == template_id and template.get('created_by') == current_user.id)
        ]
        
        if len(SEARCH_TEMPLATES) == original_count:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="搜索模板不存在或无权限删除"
            )
        
        return {
            "success": True,
            "data": {"message": "搜索模板删除成功"}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除搜索模板失败: {str(e)}"
        )

@router.post("/clean")
async def clean_data(
    request: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """智能数据清洗"""
    try:
        data = request.get('data', [])
        rules = request.get('rules', [])
        
        if not data:
            return {
                "success": True,
                "data": {
                    "cleaned_data": [],
                    "processed_count": 0,
                    "rules_applied": len(rules),
                    "processing_summary": []
                }
            }
        
        cleaned_data = data.copy()
        processing_summary = []
        
        # 应用智能清洗规则
        for rule in rules:
            if not rule.get('enabled', True):
                continue
                
            rule_type = rule.get('type')
            original_count = len(cleaned_data)
            
            if rule_type == 'field_mapping':
                mapping_rules = rule.get('mapping_rules', {})
                cleaned_data = DataCleaningEngine.field_mapping_transform(cleaned_data, mapping_rules)
                processing_summary.append({
                    'rule': rule.get('name', 'Field Mapping'),
                    'type': rule_type,
                    'action': f'映射了 {len(mapping_rules)} 个字段',
                    'before_count': original_count,
                    'after_count': len(cleaned_data)
                })
            
            elif rule_type == 'smart_deduplication':
                config = {
                    'field': rule.get('field', 'asset_value'),
                    'strategy': rule.get('strategy', 'keep_highest_score'),
                    'similarity_threshold': rule.get('similarity_threshold', 0.9)
                }
                cleaned_data = DataCleaningEngine.smart_deduplication(cleaned_data, config)
                processing_summary.append({
                    'rule': rule.get('name', 'Smart Deduplication'),
                    'type': rule_type,
                    'action': f'去重策略: {config["strategy"]}，字段: {config["field"]}',
                    'before_count': original_count,
                    'after_count': len(cleaned_data),
                    'removed_count': original_count - len(cleaned_data)
                })
            
            elif rule_type == 'data_enrichment':
                enrichment_rules = rule.get('enrichment_rules', [])
                cleaned_data = DataCleaningEngine.data_enrichment(cleaned_data, enrichment_rules)
                processing_summary.append({
                    'rule': rule.get('name', 'Data Enrichment'),
                    'type': rule_type,
                    'action': f'应用了 {len(enrichment_rules)} 个增强规则',
                    'before_count': original_count,
                    'after_count': len(cleaned_data)
                })
            
            elif rule_type == 'data_validation':
                validation_rules = rule.get('validation_rules', [])
                validation_result = DataCleaningEngine.data_validation(cleaned_data, validation_rules)
                cleaned_data = validation_result['valid_items']
                processing_summary.append({
                    'rule': rule.get('name', 'Data Validation'),
                    'type': rule_type,
                    'action': f'验证 {len(validation_rules)} 个规则',
                    'before_count': original_count,
                    'after_count': len(cleaned_data),
                    'invalid_count': len(validation_result['invalid_items']),
                    'validation_errors': validation_result['validation_errors'][:10]  # 只返回前10个错误
                })
        
        return {
            "success": True,
            "data": {
                "cleaned_data": cleaned_data,
                "processed_count": len(cleaned_data),
                "original_count": len(data),
                "rules_applied": len([r for r in rules if r.get('enabled', True)]),
                "processing_summary": processing_summary
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据清洗失败: {str(e)}"
        )

@router.get("/model-types")
async def get_model_types(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取动态模型类型用于字段映射"""
    try:
        query = select(ModelType).options(selectinload(ModelType.fields)).where(ModelType.is_active == True)
        result = await db.execute(query)
        model_types = result.scalars().all()
        
        formatted_types = []
        for model_type in model_types:
            formatted_types.append({
                "id": str(model_type.id),
                "name": model_type.name,
                "display_name": model_type.display_name,
                "description": model_type.description,
                "fields": [{
                    "id": str(field.id),
                    "field_name": field.field_name,
                    "field_type": field.field_type,
                    "display_name": field.display_name,
                    "description": field.description,
                    "is_required": field.is_required
                } for field in model_type.fields]
            })
        
        return {
            "success": True,
            "data": formatted_types
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型类型失败: {str(e)}"
        )

@router.post("/field-mappings")
async def create_field_mapping(
    mapping_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建字段映射配置"""
    try:
        await ensure_field_mapping_table(db)
        
        insert_query = text("""
            INSERT INTO field_mappings (name, description, source_index, target_model_type_id, mapping_rules, created_by_user_id)
            VALUES (:name, :description, :source_index, :target_model_type_id, :mapping_rules, :user_id)
            RETURNING id
        """)
        
        result = await db.execute(insert_query, {
            "name": mapping_data['name'],
            "description": mapping_data.get('description', ''),
            "source_index": mapping_data['source_index'],
            "target_model_type_id": mapping_data.get('target_model_type_id'),
            "mapping_rules": json.dumps(mapping_data['mapping_rules']),
            "user_id": current_user.id
        })
        
        mapping_id = result.scalar_one()
        await db.commit()
        
        return {
            "success": True,
            "data": {
                "mapping_id": str(mapping_id),
                "message": "字段映射创建成功"
            }
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建字段映射失败: {str(e)}"
        )

@router.get("/field-mappings")
async def get_field_mappings(
    source_index: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取字段映射配置"""
    try:
        await ensure_field_mapping_table(db)
        
        query_str = """
            SELECT id, name, description, source_index, target_model_type_id, mapping_rules, created_at
            FROM field_mappings 
            WHERE created_by_user_id = :user_id
        """
        params = {"user_id": current_user.id}
        
        if source_index:
            query_str += " AND source_index = :source_index"
            params["source_index"] = source_index
            
        query_str += " ORDER BY created_at DESC"
        
        result = await db.execute(text(query_str), params)
        mappings = []
        
        for row in result:
            mappings.append({
                "id": str(row.id),
                "name": row.name,
                "description": row.description,
                "source_index": row.source_index,
                "target_model_type_id": str(row.target_model_type_id) if row.target_model_type_id else None,
                "mapping_rules": json.loads(row.mapping_rules),
                "created_at": row.created_at.isoformat()
            })
        
        return {
            "success": True,
            "data": mappings
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取字段映射失败: {str(e)}"
        )

@router.post("/cleaning-rules")
async def create_cleaning_rule(
    rule_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建数据清洗规则"""
    try:
        await ensure_cleaning_rules_table(db)
        
        insert_query = text("""
            INSERT INTO data_cleaning_rules (name, description, rule_type, source_field, target_field, 
                                           mapping_config, transformation_rules, conditions, priority, created_by_user_id)
            VALUES (:name, :description, :rule_type, :source_field, :target_field, 
                    :mapping_config, :transformation_rules, :conditions, :priority, :user_id)
            RETURNING id
        """)
        
        result = await db.execute(insert_query, {
            "name": rule_data['name'],
            "description": rule_data.get('description', ''),
            "rule_type": rule_data['rule_type'],
            "source_field": rule_data['source_field'],
            "target_field": rule_data.get('target_field'),
            "mapping_config": json.dumps(rule_data.get('mapping_config', {})),
            "transformation_rules": json.dumps(rule_data.get('transformation_rules', {})),
            "conditions": json.dumps(rule_data.get('conditions', {})),
            "priority": rule_data.get('priority', 0),
            "user_id": current_user.id
        })
        
        rule_id = result.scalar_one()
        await db.commit()
        
        return {
            "success": True,
            "data": {
                "rule_id": str(rule_id),
                "message": "清洗规则创建成功"
            }
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建清洗规则失败: {str(e)}"
        )

@router.get("/cleaning-rules")
async def get_cleaning_rules(
    rule_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取清洗规则"""
    try:
        await ensure_cleaning_rules_table(db)
        
        query_str = """
            SELECT id, name, description, rule_type, source_field, target_field, 
                   mapping_config, transformation_rules, conditions, priority, is_active, created_at
            FROM data_cleaning_rules 
            WHERE created_by_user_id = :user_id
        """
        params = {"user_id": current_user.id}
        
        if rule_type:
            query_str += " AND rule_type = :rule_type"
            params["rule_type"] = rule_type
            
        query_str += " ORDER BY priority DESC, created_at DESC"
        
        result = await db.execute(text(query_str), params)
        rules = []
        
        for row in result:
            rules.append({
                "id": str(row.id),
                "name": row.name,
                "description": row.description,
                "rule_type": row.rule_type,
                "source_field": row.source_field,
                "target_field": row.target_field,
                "mapping_config": json.loads(row.mapping_config) if row.mapping_config else {},
                "transformation_rules": json.loads(row.transformation_rules) if row.transformation_rules else {},
                "conditions": json.loads(row.conditions) if row.conditions else {},
                "priority": row.priority,
                "is_active": row.is_active,
                "created_at": row.created_at.isoformat()
            })
        
        return {
            "success": True,
            "data": rules
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取清洗规则失败: {str(e)}"
        )

@router.post("/export")
async def export_data(
    request: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """导出数据"""
    try:
        data = request.get('data', [])
        format_type = request.get('format', 'json').lower()
        
        if not data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有数据可导出"
            )
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if format_type == 'json':
            content = json.dumps(data, indent=2, ensure_ascii=False)
            filename = f"search_results_{timestamp}.json"
            content_type = "application/json"
        
        elif format_type == 'csv':
            # 提取扁平化数据用于CSV
            flattened_data = []
            for item in data:
                source = item.get('_source', {})
                flat_item = {
                    'id': item.get('_id', ''),
                    'index': item.get('_index', ''),
                    'score': item.get('_score', ''),
                    'asset_value': source.get('asset_value') or source.get('data', {}).get('value', ''),
                    'asset_type': source.get('asset_type') or source.get('metadata', {}).get('asset_type', ''),
                    'confidence': source.get('confidence', ''),
                    'status': source.get('status', ''),
                    'discovered_at': source.get('discovered_at') or source.get('@timestamp', ''),
                    'tags': ','.join(source.get('tags', [])) if source.get('tags') else ''
                }
                flattened_data.append(flat_item)
            
            # 转换为CSV
            output = io.StringIO()
            if flattened_data:
                writer = csv.DictWriter(output, fieldnames=flattened_data[0].keys())
                writer.writeheader()
                writer.writerows(flattened_data)
            
            content = output.getvalue()
            filename = f"search_results_{timestamp}.csv"
            content_type = "text/csv"
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的导出格式: {format_type}"
            )
        
        return {
            "success": True,
            "data": {
                "content": content,
                "filename": filename,
                "content_type": content_type,
                "size": len(content)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据导出失败: {str(e)}"
        )

@router.get("/indices")
async def get_available_indices(
    current_user: User = Depends(get_current_user),
    es_client = Depends(get_es_client)
):
    """获取可用的ES索引"""
    try:
        # 获取所有索引
        indices = await es_client.cat.indices(format='json')
        
        # 过滤和格式化索引信息
        available_indices = []
        for index in indices:
            index_name = index.get('index', '')
            
            # 跳过系统索引
            if index_name.startswith('.'):
                continue
            
            available_indices.append({
                'name': index_name,
                'docs_count': int(index.get('docs.count', 0)),
                'store_size': index.get('store.size', '0b'),
                'status': index.get('status', 'unknown')
            })
        
        # 按文档数量排序
        available_indices.sort(key=lambda x: x['docs_count'], reverse=True)
        
        return {
            "success": True,
            "data": available_indices
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取索引列表失败: {str(e)}"
        )

@router.get("/query-suggestions")
async def get_query_suggestions(
    query_type: str = "basic",
    current_user: User = Depends(get_current_user)
):
    """获取查询建议"""
    try:
        suggestions = {
            "basic": [
                {
                    "name": "全部匹配",
                    "query": {"match_all": {}},
                    "description": "匹配所有文档"
                },
                {
                    "name": "文本搜索",
                    "query": {"match": {"asset_value": "example.com"}},
                    "description": "在asset_value字段中搜索文本"
                },
                {
                    "name": "精确匹配",
                    "query": {"term": {"asset_type": "subdomain"}},
                    "description": "精确匹配asset_type字段"
                }
            ],
            "advanced": [
                {
                    "name": "布尔查询",
                    "query": {
                        "bool": {
                            "must": [{"term": {"asset_type": "subdomain"}}],
                            "filter": [{"range": {"confidence": {"gte": 0.8}}}]
                        }
                    },
                    "description": "组合多个查询条件"
                },
                {
                    "name": "范围查询",
                    "query": {
                        "range": {
                            "@timestamp": {
                                "gte": "now-7d",
                                "lte": "now"
                            }
                        }
                    },
                    "description": "查询时间范围内的文档"
                },
                {
                    "name": "通配符查询",
                    "query": {"wildcard": {"asset_value": "*.example.com"}},
                    "description": "使用通配符匹配"
                }
            ],
            "aggregation": [
                {
                    "name": "按类型聚合",
                    "query": {
                        "size": 0,
                        "aggs": {
                            "asset_types": {
                                "terms": {"field": "asset_type"}
                            }
                        }
                    },
                    "description": "按资产类型分组统计"
                },
                {
                    "name": "时间直方图",
                    "query": {
                        "size": 0,
                        "aggs": {
                            "timeline": {
                                "date_histogram": {
                                    "field": "@timestamp",
                                    "calendar_interval": "day"
                                }
                            }
                        }
                    },
                    "description": "按时间分布统计"
                }
            ]
        }
        
        return {
            "success": True,
            "data": suggestions.get(query_type, suggestions["basic"])
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取查询建议失败: {str(e)}"
        )