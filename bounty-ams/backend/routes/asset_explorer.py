"""
资产探索器 API路由
统一的资产搜索和管理接口，支持多ES索引查询
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from auth import get_current_user
from models_dynamic import User
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/asset-explorer", tags=["asset-explorer"])


@router.get("/search")
async def search_assets(
    q: Optional[str] = Query(None, description="搜索关键词"),
    asset_type: Optional[str] = Query(None, description="资产类型"),
    data_source: Optional[str] = Query(None, description="数据源"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """统一的资产搜索接口"""
    try:
        # 构建查询
        query = {"match_all": {}}
        
        if q or asset_type or data_source:
            bool_query = {"bool": {"must": []}}
            
            if q:
                bool_query["bool"]["must"].append({
                    "multi_match": {
                        "query": q,
                        "fields": [
                            "asset_value^3", "asset_value.keyword^2", 
                            "asset_host^2", "asset_host.keyword",
                            "data.value^3", "data.name^2", 
                            "full_text", "tags", "metadata.*"
                        ],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                })
            
            if asset_type:
                bool_query["bool"]["must"].append({
                    "bool": {
                        "should": [
                            {"term": {"asset_type": asset_type}},
                            {"term": {"metadata.asset_type": asset_type}}
                        ]
                    }
                })
            
            if data_source:
                bool_query["bool"]["must"].append({
                    "bool": {
                        "should": [
                            {"term": {"source_task_type": data_source}},
                            {"term": {"metadata.data_source": data_source}}
                        ]
                    }
                })
            
            query = bool_query
        
        # 计算分页
        from_offset = (page - 1) * size
        
        # 搜索多个资产索引
        indices = [
            "enhanced_asset-2025-07",
            "unified-assets-2025-07", 
            "assets-2025-07",
            "assets-v3-2025-07",
            "assets"
        ]
        
        # 执行搜索
        response = await es_client.search(
            index=",".join(indices),
            body={
                "query": query,
                "from": from_offset,
                "size": size,
                "sort": [{"@timestamp": {"order": "desc", "missing": "_last"}}, {"_score": {"order": "desc"}}]
            },
            ignore_unavailable=True
        )
        
        # 处理结果，统一数据格式
        assets = []
        for hit in response["hits"]["hits"]:
            source = hit["_source"]
            
            # 统一数据格式，适配不同的索引结构
            unified_asset = {
                "id": hit["_id"],
                "score": hit["_score"],
                "index": hit["_index"],
                "source": {
                    "data": {
                        "value": source.get("asset_value") or source.get("data", {}).get("value") or "N/A",
                        "name": source.get("data", {}).get("name") or source.get("asset_host") or "",
                        "tags": source.get("tags", []) or source.get("data", {}).get("tags", [])
                    },
                    "metadata": {
                        "asset_type": source.get("asset_type") or source.get("metadata", {}).get("asset_type") or "unknown",
                        "data_source": source.get("source_task_type") or source.get("metadata", {}).get("data_source") or "unknown",
                        "quality_level": source.get("confidence") or source.get("metadata", {}).get("quality_level") or "medium",
                        "confidence": source.get("confidence") or source.get("metadata", {}).get("confidence") or 0.8,
                        "created_at": source.get("discovered_at") or source.get("@timestamp") or source.get("metadata", {}).get("created_at") or "",
                        "status": source.get("status", "active")
                    },
                    "original": source  # 保留原始数据用于详情查看
                }
            }
            assets.append(unified_asset)
        
        total = response["hits"]["total"]["value"]
        
        return {
            "success": True,
            "data": {
                "assets": assets,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "took": response["took"]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/statistics")
async def get_statistics(
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """统一的统计信息接口"""
    try:
        # 查找可用的资产索引
        indices = [
            "enhanced_asset-2025-07",
            "unified-assets-2025-07", 
            "assets-2025-07",
            "assets-v3-2025-07",
            "assets"
        ]
        
        # 聚合查询用于统计
        agg_response = await es_client.search(
            index=",".join(indices),
            body={
                "size": 0,
                "aggs": {
                    "asset_types": {
                        "terms": {
                            "script": {
                                "source": "params._source.asset_type ?: params._source.metadata?.asset_type ?: 'unknown'",
                                "lang": "painless"
                            },
                            "size": 20
                        }
                    },
                    "data_sources": {
                        "terms": {
                            "script": {
                                "source": "params._source.source_task_type ?: params._source.metadata?.data_source ?: 'unknown'",
                                "lang": "painless"
                            },
                            "size": 10
                        }
                    },
                    "quality_levels": {
                        "terms": {
                            "script": {
                                "source": "String conf = params._source.confidence?.toString() ?: params._source.metadata?.quality_level ?: 'medium'; if (conf.contains('high') || (conf.matches('[0-9.]+') && Double.parseDouble(conf) > 0.8)) { return 'high'; } else if (conf.contains('low') || (conf.matches('[0-9.]+') && Double.parseDouble(conf) < 0.5)) { return 'low'; } else { return 'medium'; }",
                                "lang": "painless"
                            },
                            "size": 5
                        }
                    }
                }
            },
            ignore_unavailable=True
        )
        
        total = agg_response["hits"]["total"]["value"]
        aggs = agg_response["aggregations"]
        
        return {
            "success": True,
            "data": {
                "total_assets": total,
                "asset_types": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["asset_types"]["buckets"]
                ],
                "data_sources": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["data_sources"]["buckets"]
                ],
                "quality_levels": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["quality_levels"]["buckets"]
                ]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/ingest")
async def ingest_assets(
    assets_data: List[Dict[str, Any]],
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """统一的资产添加接口"""
    try:
        success_count = 0
        errors = []
        
        # 查找可用的资产索引
        indices = [
            "enhanced_asset-2025-07",
            "unified-assets-2025-07", 
            "assets-2025-07",
            "assets-v3-2025-07",
            "assets"
        ]
        
        # 尝试从最新的索引插入
        target_index = "enhanced_asset-2025-07"
        
        for i, asset_data in enumerate(assets_data):
            try:
                # 构建标准资产文档，适配 enhanced_asset 索引结构
                doc = {
                    "asset_value": asset_data.get("value", ""),
                    "asset_type": asset_data.get("asset_type", "other"),
                    "asset_host": asset_data.get("name") or asset_data.get("value", ""),
                    "confidence": asset_data.get("confidence", "medium"),
                    "status": "active",
                    "tags": asset_data.get("tags", []),
                    "source_task_type": asset_data.get("data_source", "manual_import"),
                    "discovered_at": datetime.utcnow().isoformat(),
                    "platform_id": asset_data.get("platform_id"),
                    "project_id": asset_data.get("project_id"),
                    "metadata": {
                        "description": asset_data.get("description"),
                        "attributes": asset_data.get("attributes", {}),
                        "processing_notes": "手动添加"
                    },
                    "@timestamp": datetime.utcnow().isoformat()
                }
                
                # 插入到ES
                asset_id = f"manual_{datetime.utcnow().timestamp()}_{i}"
                await es_client.index(
                    index=target_index,
                    id=asset_id,
                    body=doc
                )
                
                success_count += 1
                
            except Exception as e:
                errors.append(f"资产 {i}: {str(e)}")
        
        # 刷新索引
        await es_client.indices.refresh(index=target_index)
        
        return {
            "success": True,
            "data": {
                "total_processed": len(assets_data),
                "success_count": success_count,
                "error_count": len(errors),
                "errors": errors
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"摄取失败: {str(e)}"
        )


@router.get("/{asset_id}")
async def get_asset(
    asset_id: str,
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """获取单个资产"""
    try:
        # 查找可用的资产索引
        indices = [
            "enhanced_asset-2025-07",
            "unified-assets-2025-07", 
            "assets-2025-07",
            "assets-v3-2025-07",
            "assets"
        ]
        
        response = await es_client.get(
            index=",".join(indices),
            id=asset_id,
            ignore_unavailable=True
        )
        
        return {
            "success": True,
            "data": {
                "id": response["_id"],
                "source": response["_source"]
            }
        }
        
    except Exception as e:
        if "not_found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取资产失败: {str(e)}"
            )
