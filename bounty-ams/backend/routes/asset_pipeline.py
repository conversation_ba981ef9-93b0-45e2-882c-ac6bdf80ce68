"""
资产聚合管道 API路由
提供统一资产聚合、管理和查询功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select
from typing import List, Dict, Any, Optional
import json
from datetime import datetime, timedelta

from database import get_db
from auth import get_current_user, get_current_admin_user
from models_dynamic import User
from elasticsearch_client import get_es_client
from asset_pipeline import AssetAggregationPipeline, run_asset_aggregation

router = APIRouter(prefix="/asset-pipeline", tags=["asset-pipeline"])


@router.post("/run-aggregation")
async def trigger_asset_aggregation(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
    es_client = Depends(get_es_client)
):
    """触发资产聚合流程"""
    try:
        # 后台运行聚合任务
        background_tasks.add_task(run_asset_aggregation)
        
        return {
            "success": True,
            "message": "资产聚合任务已启动，将在后台运行",
            "started_by": current_user.username,
            "started_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动聚合任务失败: {str(e)}"
        )


@router.get("/aggregation-status")
async def get_aggregation_status(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取聚合状态"""
    try:
        # 检查统一资产表状态
        stats_query = text("""
            SELECT 
                COUNT(*) as total_assets,
                COUNT(DISTINCT asset_type) as asset_types,
                COUNT(DISTINCT platform_id) as platforms,
                COUNT(DISTINCT project_id) as projects,
                AVG(confidence) as avg_confidence,
                MAX(updated_at) as last_updated
            FROM unified_assets
        """)
        
        result = await db.execute(stats_query)
        stats = result.fetchone()
        
        # 按类型分组统计
        type_stats_query = text("""
            SELECT 
                asset_type,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence,
                COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_count
            FROM unified_assets
            GROUP BY asset_type
            ORDER BY count DESC
        """)
        
        type_result = await db.execute(type_stats_query)
        type_stats = []
        for row in type_result.fetchall():
            type_stats.append({
                'asset_type': row[0],
                'count': row[1], 
                'avg_confidence': row[2],
                'verified_count': row[3]
            })
        
        # 按状态分组统计
        status_stats_query = text("""
            SELECT 
                status,
                COUNT(*) as count
            FROM unified_assets
            GROUP BY status
        """)
        
        status_result = await db.execute(status_stats_query)
        status_stats = []
        for row in status_result.fetchall():
            status_stats.append({
                'status': row[0],
                'count': row[1]
            })
        
        return {
            "success": True,
            "data": {
                "overview": {
                    "total_assets": stats.total_assets if stats else 0,
                    "asset_types": stats.asset_types if stats else 0,
                    "platforms": stats.platforms if stats else 0,
                    "projects": stats.projects if stats else 0,
                    "avg_confidence": round(float(stats.avg_confidence), 2) if stats and stats.avg_confidence else 0,
                    "last_updated": stats.last_updated.isoformat() if stats and stats.last_updated else None
                },
                "by_type": type_stats,
                "by_status": status_stats
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取聚合状态失败: {str(e)}"
        )


@router.get("/unified-assets")
async def get_unified_assets(
    asset_type: Optional[str] = None,
    status: Optional[str] = None,
    platform_id: Optional[str] = None,
    project_id: Optional[str] = None,
    search: Optional[str] = None,
    confidence_min: Optional[float] = None,
    page: int = 1,
    size: int = 50,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """查询统一资产"""
    try:
        # 构建查询条件
        conditions = ["1=1"]
        params = {}
        
        if asset_type:
            conditions.append("asset_type = :asset_type")
            params["asset_type"] = asset_type
            
        if status:
            conditions.append("status = :status")
            params["status"] = status
            
        if platform_id:
            conditions.append("platform_id = :platform_id")
            params["platform_id"] = platform_id
            
        if project_id:
            conditions.append("project_id = :project_id")
            params["project_id"] = project_id
            
        if search:
            conditions.append("(asset_value ILIKE :search OR tags::text ILIKE :search)")
            params["search"] = f"%{search}%"
            
        if confidence_min is not None:
            conditions.append("confidence >= :confidence_min")
            params["confidence_min"] = confidence_min
        
        where_clause = " AND ".join(conditions)
        
        # 计算总数
        count_query = text(f"""
            SELECT COUNT(*) FROM unified_assets 
            WHERE {where_clause}
        """)
        
        total_result = await db.execute(count_query, params)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * size
        params["offset"] = offset
        params["size"] = size
        
        query = text(f"""
            SELECT 
                asset_id, fingerprint_hash, asset_type, asset_value,
                asset_host, asset_port, asset_service, confidence,
                status, source, platform_id, project_id,
                discovered_at, verified_at, updated_at, tags, metadata
            FROM unified_assets 
            WHERE {where_clause}
            ORDER BY updated_at DESC
            LIMIT :size OFFSET :offset
        """)
        
        result = await db.execute(query, params)
        assets = []
        
        for row in result.fetchall():
            asset = dict(row)
            # 解析JSON字段
            if asset['tags']:
                asset['tags'] = json.loads(asset['tags']) if isinstance(asset['tags'], str) else asset['tags']
            if asset['metadata']:
                asset['metadata'] = json.loads(asset['metadata']) if isinstance(asset['metadata'], str) else asset['metadata']
            
            # 格式化时间
            for time_field in ['discovered_at', 'verified_at', 'updated_at']:
                if asset[time_field]:
                    asset[time_field] = asset[time_field].isoformat()
            
            assets.append(asset)
        
        return {
            "success": True,
            "data": {
                "assets": assets,
                "pagination": {
                    "total": total,
                    "page": page,
                    "size": size,
                    "pages": (total + size - 1) // size
                }
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询统一资产失败: {str(e)}"
        )


@router.put("/unified-assets/{asset_id}")
async def update_unified_asset(
    asset_id: str,
    update_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新统一资产"""
    try:
        # 允许更新的字段
        allowed_fields = [
            'status', 'confidence', 'tags', 'metadata', 
            'verified_at', 'platform_id', 'project_id'
        ]
        
        update_fields = []
        params = {"asset_id": asset_id}
        
        for field, value in update_data.items():
            if field in allowed_fields:
                if field in ['tags', 'metadata']:
                    value = json.dumps(value) if value else None
                elif field == 'verified_at' and value:
                    # 如果设置了verified_at，同时更新status
                    if 'status' not in update_data:
                        update_fields.append("status = 'verified'")
                
                update_fields.append(f"{field} = :{field}")
                params[field] = value
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有有效的更新字段"
            )
        
        # 添加更新时间
        update_fields.append("updated_at = NOW()")
        
        update_query = text(f"""
            UPDATE unified_assets 
            SET {', '.join(update_fields)}
            WHERE asset_id = :asset_id
            RETURNING asset_id
        """)
        
        result = await db.execute(update_query, params)
        updated_id = result.scalar_one_or_none()
        
        if not updated_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        
        await db.commit()
        
        return {
            "success": True,
            "message": "资产更新成功",
            "asset_id": updated_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新资产失败: {str(e)}"
        )


@router.delete("/unified-assets/{asset_id}")
async def delete_unified_asset(
    asset_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db)
):
    """删除统一资产"""
    try:
        delete_query = text("""
            DELETE FROM unified_assets 
            WHERE asset_id = :asset_id
            RETURNING asset_id
        """)
        
        result = await db.execute(delete_query, {"asset_id": asset_id})
        deleted_id = result.scalar_one_or_none()
        
        if not deleted_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        
        await db.commit()
        
        return {
            "success": True,
            "message": "资产删除成功",
            "asset_id": deleted_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产失败: {str(e)}"
        )


@router.post("/unified-assets/bulk-update")
async def bulk_update_assets(
    update_request: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量更新资产"""
    try:
        asset_ids = update_request.get('asset_ids', [])
        update_data = update_request.get('update_data', {})
        
        if not asset_ids or not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少资产ID或更新数据"
            )
        
        # 构建批量更新查询
        allowed_fields = ['status', 'confidence', 'tags', 'metadata', 'platform_id', 'project_id']
        update_fields = []
        params = {"asset_ids": tuple(asset_ids)}
        
        for field, value in update_data.items():
            if field in allowed_fields:
                if field in ['tags', 'metadata']:
                    value = json.dumps(value) if value else None
                
                update_fields.append(f"{field} = :{field}")
                params[field] = value
        
        if not update_fields:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有有效的更新字段"
            )
        
        update_fields.append("updated_at = NOW()")
        
        update_query = text(f"""
            UPDATE unified_assets 
            SET {', '.join(update_fields)}
            WHERE asset_id = ANY(:asset_ids)
        """)
        
        result = await db.execute(update_query, params)
        updated_count = result.rowcount
        
        await db.commit()
        
        return {
            "success": True,
            "message": f"成功更新 {updated_count} 个资产",
            "updated_count": updated_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新失败: {str(e)}"
        )


@router.get("/asset-relationships/{asset_id}")
async def get_asset_relationships(
    asset_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取资产关联关系"""
    try:
        # 获取主资产信息
        main_query = text("""
            SELECT * FROM unified_assets WHERE asset_id = :asset_id
        """)
        
        main_result = await db.execute(main_query, {"asset_id": asset_id})
        main_asset = main_result.fetchone()
        
        if not main_asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        
        # 查找相关资产（基于域名关系）
        if main_asset.asset_type in ['domain', 'subdomain']:
            related_query = text("""
                SELECT * FROM unified_assets 
                WHERE (asset_value LIKE :pattern OR :asset_value LIKE '%' || asset_value || '%')
                AND asset_id != :asset_id
                AND asset_type IN ('domain', 'subdomain', 'url')
                LIMIT 20
            """)
            
            pattern = f"%.{main_asset.asset_value}%" if main_asset.asset_type == 'domain' else f"%{main_asset.asset_value}%"
            
            related_result = await db.execute(related_query, {
                "pattern": pattern,
                "asset_value": main_asset.asset_value,
                "asset_id": asset_id
            })
            
            related_assets = [dict(row) for row in related_result.fetchall()]
        else:
            related_assets = []
        
        return {
            "success": True,
            "data": {
                "main_asset": dict(main_asset),
                "related_assets": related_assets,
                "relationship_count": len(related_assets)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产关系失败: {str(e)}"
        )