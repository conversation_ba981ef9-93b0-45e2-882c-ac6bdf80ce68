"""
统一资产聚合管道
负责将多种扫描器结果聚合成统一的资产数据，然后同步到PostgreSQL
"""

import asyncio
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, insert, update
from elasticsearch import AsyncElasticsearch

from database import get_db
from elasticsearch_client import get_es_client
from models_dynamic import ModelType, ModelField


@dataclass
class UnifiedAsset:
    """统一资产模型"""
    asset_id: str
    fingerprint_hash: str
    asset_type: str  # domain, subdomain, ip, port, service, url, certificate
    asset_value: str  # 主要值
    asset_host: Optional[str] = None
    asset_port: Optional[int] = None
    asset_service: Optional[str] = None
    
    # 置信度和状态
    confidence: float = 0.5
    status: str = 'discovered'  # discovered, verified, validated, false_positive
    
    # 来源信息
    source: str = 'unknown'
    source_task_id: Optional[str] = None
    source_task_type: Optional[str] = None
    platform_id: Optional[str] = None
    project_id: Optional[str] = None
    
    # 时间戳
    discovered_at: datetime = None
    verified_at: Optional[datetime] = None
    updated_at: datetime = None
    
    # 标签和元数据
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    # 关联信息
    parent_asset_id: Optional[str] = None
    child_asset_ids: List[str] = None
    related_asset_ids: List[str] = None
    
    def __post_init__(self):
        if self.discovered_at is None:
            self.discovered_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.child_asset_ids is None:
            self.child_asset_ids = []
        if self.related_asset_ids is None:
            self.related_asset_ids = []
    
    def generate_fingerprint(self) -> str:
        """生成资产指纹用于去重"""
        fingerprint_data = f"{self.asset_type}:{self.asset_value}"
        if self.asset_host:
            fingerprint_data += f":{self.asset_host}"
        if self.asset_port:
            fingerprint_data += f":{self.asset_port}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()


class AssetAggregationPipeline:
    """资产聚合管道"""
    
    def __init__(self, es_client: AsyncElasticsearch, db: AsyncSession):
        self.es_client = es_client
        self.db = db
        self.unified_index = "unified_assets"
        self.raw_indices = [
            "enhanced_asset-*",
            "assets-*", 
            "unified-assets-*"
        ]
    
    async def ensure_unified_index(self):
        """确保统一索引存在"""
        mapping = {
            "mappings": {
                "properties": {
                    "asset_id": {"type": "keyword"},
                    "fingerprint_hash": {"type": "keyword"},
                    "asset_type": {"type": "keyword"},
                    "asset_value": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "asset_host": {"type": "keyword"},
                    "asset_port": {"type": "integer"},
                    "asset_service": {"type": "keyword"},
                    "confidence": {"type": "float"},
                    "status": {"type": "keyword"},
                    "source": {"type": "keyword"},
                    "source_task_id": {"type": "keyword"},
                    "source_task_type": {"type": "keyword"},
                    "platform_id": {"type": "keyword"},
                    "project_id": {"type": "keyword"},
                    "discovered_at": {"type": "date"},
                    "verified_at": {"type": "date"},
                    "updated_at": {"type": "date"},
                    "tags": {"type": "keyword"},
                    "metadata": {"type": "object", "enabled": False},
                    "parent_asset_id": {"type": "keyword"},
                    "child_asset_ids": {"type": "keyword"},
                    "related_asset_ids": {"type": "keyword"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        # 检查索引是否存在
        if not await self.es_client.indices.exists(index=self.unified_index):
            await self.es_client.indices.create(index=self.unified_index, body=mapping)
            print(f"Created unified index: {self.unified_index}")
    
    async def collect_raw_assets(self) -> List[Dict[str, Any]]:
        """从各个原始索引收集资产数据"""
        raw_assets = []
        
        for index_pattern in self.raw_indices:
            try:
                query = {
                    "query": {"match_all": {}},
                    "size": 10000,  # 分批处理
                    "sort": [{"_timestamp": {"order": "desc"}}] if "_timestamp" in index_pattern else []
                }
                
                response = await self.es_client.search(
                    index=index_pattern,
                    body=query,
                    ignore_unavailable=True
                )
                
                hits = response.get('hits', {}).get('hits', [])
                for hit in hits:
                    raw_asset = {
                        '_id': hit['_id'],
                        '_index': hit['_index'],
                        '_source': hit['_source']
                    }
                    raw_assets.append(raw_asset)
                    
                print(f"Collected {len(hits)} assets from {index_pattern}")
                
            except Exception as e:
                print(f"Error collecting from {index_pattern}: {e}")
                continue
        
        return raw_assets
    
    def normalize_asset(self, raw_asset: Dict[str, Any]) -> Optional[UnifiedAsset]:
        """将原始资产数据标准化为统一资产模型"""
        source = raw_asset['_source']
        index = raw_asset['_index']
        
        try:
            # 根据不同索引的数据结构进行转换
            if 'enhanced_asset' in index:
                return self._normalize_enhanced_asset(source)
            elif 'unified-assets' in index:
                return self._normalize_unified_asset(source)
            elif 'assets' in index:
                return self._normalize_legacy_asset(source)
            else:
                return self._normalize_generic_asset(source)
                
        except Exception as e:
            print(f"Error normalizing asset from {index}: {e}")
            return None
    
    def _normalize_enhanced_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化enhanced_asset数据"""
        entity_data = source.get('entity_data', {})
        
        asset = UnifiedAsset(
            asset_id=source.get('id', f"enhanced_{hash(str(source))}"),
            fingerprint_hash="",  # 将被重新计算
            asset_type=entity_data.get('asset_type', 'unknown'),
            asset_value=entity_data.get('asset_value', ''),
            asset_host=entity_data.get('asset_host'),
            asset_port=entity_data.get('asset_port'),
            asset_service=entity_data.get('asset_service'),
            confidence=self._normalize_confidence(entity_data.get('confidence', 'medium')),
            status=entity_data.get('status', 'discovered'),
            source=entity_data.get('source_task_type', 'enhanced'),
            source_task_id=entity_data.get('source_task_id'),
            source_task_type=entity_data.get('source_task_type'),
            platform_id=entity_data.get('platform_id'),
            project_id=entity_data.get('project_id'),
            discovered_at=self._parse_datetime(entity_data.get('discovered_at')),
            tags=entity_data.get('tags', []),
            metadata=entity_data.get('metadata', {})
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_unified_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化unified-assets数据"""
        asset = UnifiedAsset(
            asset_id=source.get('asset_id', f"unified_{hash(str(source))}"),
            fingerprint_hash=source.get('fingerprint_hash', ''),
            asset_type=source.get('asset_type', 'unknown'),
            asset_value=source.get('asset_value', ''),
            asset_host=source.get('asset_host'),
            asset_port=source.get('asset_port'),
            asset_service=source.get('asset_service'),
            confidence=float(source.get('confidence', 0.5)),
            status=source.get('status', 'discovered'),
            source=source.get('source', 'unified'),
            platform_id=source.get('platform_id'),
            project_id=source.get('project_id'),
            discovered_at=self._parse_datetime(source.get('discovered_at')),
            tags=source.get('tags', []),
            metadata=source.get('metadata', {})
        )
        
        if not asset.fingerprint_hash:
            asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_legacy_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化legacy assets数据"""
        asset = UnifiedAsset(
            asset_id=source.get('id', f"legacy_{hash(str(source))}"),
            fingerprint_hash="",
            asset_type=source.get('type', 'unknown'),
            asset_value=source.get('value', ''),
            confidence=0.5,
            status='discovered',
            source='legacy',
            discovered_at=self._parse_datetime(source.get('created_at')),
            tags=source.get('tags', []),
            metadata=source
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_generic_asset(self, source: Dict[str, Any]) -> UnifiedAsset:
        """标准化通用资产数据"""
        # 智能字段匹配
        asset_value = (
            source.get('asset_value') or 
            source.get('value') or 
            source.get('domain') or 
            source.get('ip') or 
            source.get('url') or 
            str(source)[:100]
        )
        
        asset_type = (
            source.get('asset_type') or 
            source.get('type') or 
            self._guess_asset_type(asset_value)
        )
        
        asset = UnifiedAsset(
            asset_id=f"generic_{hash(str(source))}",
            fingerprint_hash="",
            asset_type=asset_type,
            asset_value=asset_value,
            confidence=0.3,  # 通用数据置信度较低
            status='discovered',
            source='generic',
            discovered_at=datetime.utcnow(),
            metadata=source
        )
        
        asset.fingerprint_hash = asset.generate_fingerprint()
        return asset
    
    def _normalize_confidence(self, confidence_str: str) -> float:
        """将置信度字符串转换为数值"""
        confidence_map = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.9,
            'critical': 1.0
        }
        
        if isinstance(confidence_str, (int, float)):
            return float(confidence_str)
        
        return confidence_map.get(str(confidence_str).lower(), 0.5)
    
    def _parse_datetime(self, dt_str: Any) -> datetime:
        """解析各种格式的时间字符串"""
        if not dt_str:
            return datetime.utcnow()
        
        if isinstance(dt_str, datetime):
            return dt_str
        
        try:
            # 尝试多种时间格式
            for fmt in [
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d %H:%M:%S'
            ]:
                try:
                    return datetime.strptime(str(dt_str), fmt)
                except ValueError:
                    continue
                    
            # 如果都失败了，返回当前时间
            return datetime.utcnow()
            
        except Exception:
            return datetime.utcnow()
    
    def _guess_asset_type(self, value: str) -> str:
        """根据值猜测资产类型"""
        if not value:
            return 'unknown'
        
        value = str(value).strip()
        
        # IP地址
        if self._is_ip(value):
            return 'ip'
        
        # 域名
        if '.' in value and not value.startswith('http'):
            return 'domain' if value.count('.') <= 2 else 'subdomain'
        
        # URL
        if value.startswith(('http://', 'https://')):
            return 'url'
        
        # 端口
        if ':' in value and value.split(':')[-1].isdigit():
            return 'port'
        
        return 'unknown'
    
    def _is_ip(self, value: str) -> bool:
        """检查是否为IP地址"""
        try:
            parts = value.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    async def deduplicate_assets(self, assets: List[UnifiedAsset]) -> List[UnifiedAsset]:
        """资产去重和合并"""
        fingerprint_groups = {}
        
        # 按指纹分组
        for asset in assets:
            fingerprint = asset.fingerprint_hash
            if fingerprint not in fingerprint_groups:
                fingerprint_groups[fingerprint] = []
            fingerprint_groups[fingerprint].append(asset)
        
        deduplicated_assets = []
        
        for fingerprint, group_assets in fingerprint_groups.items():
            if len(group_assets) == 1:
                deduplicated_assets.append(group_assets[0])
            else:
                # 合并多个相同资产
                merged_asset = self._merge_assets(group_assets)
                deduplicated_assets.append(merged_asset)
        
        return deduplicated_assets
    
    def _merge_assets(self, assets: List[UnifiedAsset]) -> UnifiedAsset:
        """合并多个相同的资产"""
        # 选择置信度最高的作为基础
        base_asset = max(assets, key=lambda a: a.confidence)
        
        # 合并标签
        all_tags = set()
        for asset in assets:
            all_tags.update(asset.tags)
        base_asset.tags = list(all_tags)
        
        # 合并元数据
        for asset in assets:
            base_asset.metadata.update(asset.metadata)
        
        # 使用最新的时间戳
        base_asset.updated_at = max(asset.updated_at for asset in assets)
        
        # 如果有验证状态，优先使用
        verified_assets = [a for a in assets if a.status in ['verified', 'validated']]
        if verified_assets:
            base_asset.status = verified_assets[0].status
            base_asset.verified_at = verified_assets[0].verified_at or datetime.utcnow()
        
        return base_asset
    
    async def save_to_unified_index(self, assets: List[UnifiedAsset]):
        """保存到统一ES索引"""
        if not assets:
            return
        
        actions = []
        for asset in assets:
            action = {
                "_index": self.unified_index,
                "_id": asset.asset_id,
                "_source": asdict(asset)
            }
            actions.append(action)
        
        try:
            from elasticsearch.helpers import async_bulk
            success, failed = await async_bulk(self.es_client, actions)
            print(f"Saved {success} assets to unified index, {len(failed)} failed")
        except Exception as e:
            print(f"Error saving to unified index: {e}")
    
    async def sync_to_postgresql(self, assets: List[UnifiedAsset]):
        """同步到PostgreSQL"""
        if not assets:
            return
            
        # 确保资产表存在
        await self._ensure_assets_table()
        
        for asset in assets:
            try:
                # 检查是否已存在
                existing_query = text("""
                    SELECT id FROM unified_assets 
                    WHERE fingerprint_hash = :fingerprint_hash
                """)
                
                result = await self.db.execute(existing_query, {
                    "fingerprint_hash": asset.fingerprint_hash
                })
                
                existing = result.scalar_one_or_none()
                
                if existing:
                    # 更新现有资产
                    update_query = text("""
                        UPDATE unified_assets SET
                            asset_type = :asset_type,
                            asset_value = :asset_value,
                            asset_host = :asset_host,
                            asset_port = :asset_port,
                            asset_service = :asset_service,
                            confidence = :confidence,
                            status = :status,
                            source = :source,
                            platform_id = :platform_id,
                            project_id = :project_id,
                            verified_at = :verified_at,
                            updated_at = :updated_at,
                            tags = :tags,
                            metadata = :metadata
                        WHERE fingerprint_hash = :fingerprint_hash
                    """)
                    
                    await self.db.execute(update_query, {
                        "fingerprint_hash": asset.fingerprint_hash,
                        "asset_type": asset.asset_type,
                        "asset_value": asset.asset_value,
                        "asset_host": asset.asset_host,
                        "asset_port": asset.asset_port,
                        "asset_service": asset.asset_service,
                        "confidence": asset.confidence,
                        "status": asset.status,
                        "source": asset.source,
                        "platform_id": asset.platform_id,
                        "project_id": asset.project_id,
                        "verified_at": asset.verified_at,
                        "updated_at": asset.updated_at,
                        "tags": json.dumps(asset.tags),
                        "metadata": json.dumps(asset.metadata)
                    })
                else:
                    # 插入新资产
                    insert_query = text("""
                        INSERT INTO unified_assets (
                            asset_id, fingerprint_hash, asset_type, asset_value,
                            asset_host, asset_port, asset_service, confidence,
                            status, source, source_task_id, source_task_type,
                            platform_id, project_id, discovered_at, verified_at,
                            updated_at, tags, metadata, parent_asset_id
                        ) VALUES (
                            :asset_id, :fingerprint_hash, :asset_type, :asset_value,
                            :asset_host, :asset_port, :asset_service, :confidence,
                            :status, :source, :source_task_id, :source_task_type,
                            :platform_id, :project_id, :discovered_at, :verified_at,
                            :updated_at, :tags, :metadata, :parent_asset_id
                        )
                    """)
                    
                    await self.db.execute(insert_query, {
                        "asset_id": asset.asset_id,
                        "fingerprint_hash": asset.fingerprint_hash,
                        "asset_type": asset.asset_type,
                        "asset_value": asset.asset_value,
                        "asset_host": asset.asset_host,
                        "asset_port": asset.asset_port,
                        "asset_service": asset.asset_service,
                        "confidence": asset.confidence,
                        "status": asset.status,
                        "source": asset.source,
                        "source_task_id": asset.source_task_id,
                        "source_task_type": asset.source_task_type,
                        "platform_id": asset.platform_id,
                        "project_id": asset.project_id,
                        "discovered_at": asset.discovered_at,
                        "verified_at": asset.verified_at,
                        "updated_at": asset.updated_at,
                        "tags": json.dumps(asset.tags),
                        "metadata": json.dumps(asset.metadata),
                        "parent_asset_id": asset.parent_asset_id
                    })
                
            except Exception as e:
                print(f"Error syncing asset {asset.asset_id} to PostgreSQL: {e}")
                continue
        
        await self.db.commit()
        print(f"Synced {len(assets)} assets to PostgreSQL")
    
    async def _ensure_assets_table(self):
        """确保统一资产表存在"""
        # 分别执行每个SQL语句
        statements = [
            """
            CREATE TABLE IF NOT EXISTS unified_assets (
                id SERIAL PRIMARY KEY,
                asset_id VARCHAR(255) UNIQUE NOT NULL,
                fingerprint_hash VARCHAR(64) UNIQUE NOT NULL,
                asset_type VARCHAR(50) NOT NULL,
                asset_value TEXT NOT NULL,
                asset_host VARCHAR(255),
                asset_port INTEGER,
                asset_service VARCHAR(100),
                confidence FLOAT DEFAULT 0.5,
                status VARCHAR(50) DEFAULT 'discovered',
                source VARCHAR(100),
                source_task_id VARCHAR(255),
                source_task_type VARCHAR(100),
                platform_id VARCHAR(255),
                project_id VARCHAR(255),
                discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                verified_at TIMESTAMP WITH TIME ZONE,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                tags JSONB,
                metadata JSONB,
                parent_asset_id VARCHAR(255),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            """,
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_fingerprint ON unified_assets(fingerprint_hash)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_type ON unified_assets(asset_type)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_value ON unified_assets(asset_value)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_status ON unified_assets(status)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_platform ON unified_assets(platform_id)",
            "CREATE INDEX IF NOT EXISTS idx_unified_assets_tags ON unified_assets USING GIN(tags)"
        ]
        
        for stmt in statements:
            await self.db.execute(text(stmt))
        
        await self.db.commit()
    
    async def run_full_aggregation(self):
        """运行完整的资产聚合流程"""
        print("Starting asset aggregation pipeline...")
        
        # 1. 确保索引和表存在
        await self.ensure_unified_index()
        
        # 2. 收集原始资产数据
        raw_assets = await self.collect_raw_assets()
        print(f"Collected {len(raw_assets)} raw assets")
        
        # 3. 标准化资产数据
        normalized_assets = []
        for raw_asset in raw_assets:
            normalized = self.normalize_asset(raw_asset)
            if normalized:
                normalized_assets.append(normalized)
        
        print(f"Normalized {len(normalized_assets)} assets")
        
        # 4. 去重和合并
        deduplicated_assets = await self.deduplicate_assets(normalized_assets)
        print(f"After deduplication: {len(deduplicated_assets)} assets")
        
        # 5. 保存到统一ES索引
        await self.save_to_unified_index(deduplicated_assets)
        
        # 6. 同步到PostgreSQL
        await self.sync_to_postgresql(deduplicated_assets)
        
        print("Asset aggregation pipeline completed!")
        return len(deduplicated_assets)


# API端点和调度任务
async def run_asset_aggregation():
    """运行资产聚合任务"""
    from database import AsyncSessionLocal
    from elasticsearch_client import get_es_client
    
    async with AsyncSessionLocal() as db:
        es_client = await get_es_client()
        pipeline = AssetAggregationPipeline(es_client, db)
        return await pipeline.run_full_aggregation()


if __name__ == "__main__":
    # 测试运行
    asyncio.run(run_asset_aggregation())