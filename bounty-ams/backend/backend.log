nohup: ignoring input
/usr/local/lib/python3.10/dist-packages/pydantic/_internal/_fields.py:149: UserWarning: Field "model_type_id" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
/home/<USER>/code/code/bounty-ams/backend/main.py:31: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/home/<USER>/code/code/bounty-ams/backend/main.py:40: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
INFO:     Started server process [2187530]
INFO:     Waiting for application startup.
INFO:elastic_transport.transport:GET http://localhost:9200/ [status:200 duration:0.169s]
INFO:elasticsearch_client:Connected to Elasticsearch: 8.11.0
INFO:elastic_transport.transport:HEAD http://localhost:9200/assets [status:200 duration:0.011s]
INFO:__main__:Elasticsearch connected successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-07-15 11:01:52,320 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-07-15 11:01:52,320 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-07-15 11:01:52,324 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-07-15 11:01:52,324 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-07-15 11:01:52,325 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-07-15 11:01:52,325 INFO sqlalchemy.engine.Engine [raw sql] ()
INFO:sqlalchemy.engine.Engine:[raw sql] ()
2025-07-15 11:01:52,326 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:01:52,357 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:01:52,357 INFO sqlalchemy.engine.Engine [generated in 0.00021s] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[generated in 0.00021s] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:01:52,361 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:01:52,361 INFO sqlalchemy.engine.Engine [generated in 0.00011s] (datetime.datetime(2025, 7, 15, 3, 1, 52, 360875), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[generated in 0.00011s] (datetime.datetime(2025, 7, 15, 3, 1, 52, 360875), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:01:52,363 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:01:52,380 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:01:52,382 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:01:52,382 INFO sqlalchemy.engine.Engine [generated in 0.00019s] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[generated in 0.00019s] ('go-agent-instance-1',)
2025-07-15 11:01:52,385 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:01:52,386 INFO sqlalchemy.engine.Engine [generated in 0.00021s] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[generated in 0.00021s] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:39076 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:01:52,395 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:01:57,201 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:01:57,203 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:01:57,204 INFO sqlalchemy.engine.Engine [cached since 4.847s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 4.847s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:01:57,218 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:01:57,218 INFO sqlalchemy.engine.Engine [cached since 4.856s ago] (datetime.datetime(2025, 7, 15, 3, 1, 57, 214857), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 4.856s ago] (datetime.datetime(2025, 7, 15, 3, 1, 57, 214857), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:01:57,219 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:01:57,223 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:01:57,224 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:01:57,224 INFO sqlalchemy.engine.Engine [cached since 4.841s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 4.841s ago] ('go-agent-instance-1',)
2025-07-15 11:01:57,226 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:01:57,226 INFO sqlalchemy.engine.Engine [cached since 4.841s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 4.841s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:39076 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:01:57,228 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:02,202 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:02,204 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:02,208 INFO sqlalchemy.engine.Engine [cached since 9.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 9.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:02,224 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:02,224 INFO sqlalchemy.engine.Engine [cached since 9.863s ago] (datetime.datetime(2025, 7, 15, 3, 2, 2, 223006), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 9.863s ago] (datetime.datetime(2025, 7, 15, 3, 2, 2, 223006), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:02,226 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:02,235 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:02,236 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:02,236 INFO sqlalchemy.engine.Engine [cached since 9.854s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 9.854s ago] ('go-agent-instance-1',)
2025-07-15 11:02:02,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:02,239 INFO sqlalchemy.engine.Engine [cached since 9.853s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 9.853s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:39076 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:02,243 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:03,432 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:03,433 INFO sqlalchemy.engine.Engine 
            SELECT 
                COUNT(*) as total_assets,
                COUNT(DISTINCT asset_type) as asset_types,
                COUNT(DISTINCT platform_id) as platforms,
                COUNT(DISTINCT project_id) as projects,
                AVG(confidence) as avg_confidence,
                MAX(updated_at) as last_updated
            FROM unified_assets
        
INFO:sqlalchemy.engine.Engine:
            SELECT 
                COUNT(*) as total_assets,
                COUNT(DISTINCT asset_type) as asset_types,
                COUNT(DISTINCT platform_id) as platforms,
                COUNT(DISTINCT project_id) as projects,
                AVG(confidence) as avg_confidence,
                MAX(updated_at) as last_updated
            FROM unified_assets
        
2025-07-15 11:02:03,433 INFO sqlalchemy.engine.Engine [generated in 0.00010s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00010s] ()
2025-07-15 11:02:03,446 INFO sqlalchemy.engine.Engine 
            SELECT 
                asset_type,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence,
                COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_count
            FROM unified_assets
            GROUP BY asset_type
            ORDER BY count DESC
        
INFO:sqlalchemy.engine.Engine:
            SELECT 
                asset_type,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence,
                COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified_count
            FROM unified_assets
            GROUP BY asset_type
            ORDER BY count DESC
        
2025-07-15 11:02:03,447 INFO sqlalchemy.engine.Engine [generated in 0.00046s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00046s] ()
2025-07-15 11:02:03,448 INFO sqlalchemy.engine.Engine 
            SELECT 
                status,
                COUNT(*) as count
            FROM unified_assets
            GROUP BY status
        
INFO:sqlalchemy.engine.Engine:
            SELECT 
                status,
                COUNT(*) as count
            FROM unified_assets
            GROUP BY status
        
2025-07-15 11:02:03,449 INFO sqlalchemy.engine.Engine [generated in 0.00017s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00017s] ()
INFO:     127.0.0.1:48980 - "GET /api/asset-pipeline/aggregation-status HTTP/1.1" 200 OK
2025-07-15 11:02:03,453 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:07,203 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:07,205 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:07,206 INFO sqlalchemy.engine.Engine [cached since 14.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 14.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:07,211 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:07,211 INFO sqlalchemy.engine.Engine [cached since 14.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 7, 211150), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 14.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 7, 211150), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:07,212 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:07,221 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:07,221 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:07,221 INFO sqlalchemy.engine.Engine [cached since 14.84s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 14.84s ago] ('go-agent-instance-1',)
2025-07-15 11:02:07,223 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:07,223 INFO sqlalchemy.engine.Engine [cached since 14.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 14.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:39076 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:07,230 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:12,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:12,222 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:12,222 INFO sqlalchemy.engine.Engine [cached since 19.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 19.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:12,229 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:12,230 INFO sqlalchemy.engine.Engine [cached since 19.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 12, 229081), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 19.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 12, 229081), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:12,231 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:12,288 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:12,289 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:12,290 INFO sqlalchemy.engine.Engine [cached since 19.91s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 19.91s ago] ('go-agent-instance-1',)
2025-07-15 11:02:12,294 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:12,294 INFO sqlalchemy.engine.Engine [cached since 19.91s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 19.91s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:39076 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:12,297 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:12,859 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:12,859 INFO sqlalchemy.engine.Engine 
            SELECT COUNT(*) FROM unified_assets 
            WHERE 1=1
        
INFO:sqlalchemy.engine.Engine:
            SELECT COUNT(*) FROM unified_assets 
            WHERE 1=1
        
2025-07-15 11:02:12,859 INFO sqlalchemy.engine.Engine [generated in 0.00008s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00008s] ()
2025-07-15 11:02:12,865 INFO sqlalchemy.engine.Engine 
            SELECT 
                asset_id, fingerprint_hash, asset_type, asset_value,
                asset_host, asset_port, asset_service, confidence,
                status, source, platform_id, project_id,
                discovered_at, verified_at, updated_at, tags, metadata
            FROM unified_assets 
            WHERE 1=1
            ORDER BY updated_at DESC
            LIMIT $1 OFFSET $2
        
INFO:sqlalchemy.engine.Engine:
            SELECT 
                asset_id, fingerprint_hash, asset_type, asset_value,
                asset_host, asset_port, asset_service, confidence,
                status, source, platform_id, project_id,
                discovered_at, verified_at, updated_at, tags, metadata
            FROM unified_assets 
            WHERE 1=1
            ORDER BY updated_at DESC
            LIMIT $1 OFFSET $2
        
2025-07-15 11:02:12,865 INFO sqlalchemy.engine.Engine [generated in 0.00024s] (5, 0)
INFO:sqlalchemy.engine.Engine:[generated in 0.00024s] (5, 0)
2025-07-15 11:02:12,868 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:42158 - "GET /api/asset-pipeline/unified-assets?size=5 HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/code/code/bounty-ams/backend/routes/asset_pipeline.py", line 207, in get_unified_assets
    asset = dict(row)
ValueError: dictionary update sequence element #0 has length 41; 2 is required

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/usr/local/lib/python3.10/dist-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 66, in app
    response = await func(request)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/routing.py", line 274, in app
    raw_response = await run_endpoint_function(
  File "/usr/local/lib/python3.10/dist-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "/home/<USER>/code/code/bounty-ams/backend/routes/asset_pipeline.py", line 236, in get_unified_assets
    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
AttributeError: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
2025-07-15 11:02:17,217 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:17,220 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:17,221 INFO sqlalchemy.engine.Engine [cached since 24.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 24.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:17,238 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:17,239 INFO sqlalchemy.engine.Engine [cached since 24.88s ago] (datetime.datetime(2025, 7, 15, 3, 2, 17, 237652), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 24.88s ago] (datetime.datetime(2025, 7, 15, 3, 2, 17, 237652), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:17,254 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:17,269 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:17,270 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:17,270 INFO sqlalchemy.engine.Engine [cached since 24.89s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 24.89s ago] ('go-agent-instance-1',)
2025-07-15 11:02:17,303 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:02:17,303 INFO sqlalchemy.engine.Engine [generated in 0.00030s] (0, 'online', datetime.datetime(2025, 7, 15, 3, 2, 17, 299759, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548537}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[generated in 0.00030s] (0, 'online', datetime.datetime(2025, 7, 15, 3, 2, 17, 299759, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548537}', 'go-agent-instance-1')
2025-07-15 11:02:17,305 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:17,307 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:17,308 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:17,308 INFO sqlalchemy.engine.Engine [cached since 24.95s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 24.95s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:17,312 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:17,312 INFO sqlalchemy.engine.Engine [cached since 24.95s ago] (datetime.datetime(2025, 7, 15, 3, 2, 17, 312554), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 24.95s ago] (datetime.datetime(2025, 7, 15, 3, 2, 17, 312554), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:     127.0.0.1:39076 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:02:17,315 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:17,320 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:17,320 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:17,320 INFO sqlalchemy.engine.Engine [cached since 24.94s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 24.94s ago] ('go-agent-instance-1',)
2025-07-15 11:02:17,323 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:17,323 INFO sqlalchemy.engine.Engine [cached since 24.94s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 24.94s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:17,325 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:19,771 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:19,772 INFO sqlalchemy.engine.Engine 
            SELECT COUNT(*) FROM unified_assets 
            WHERE 1=1
        
INFO:sqlalchemy.engine.Engine:
            SELECT COUNT(*) FROM unified_assets 
            WHERE 1=1
        
2025-07-15 11:02:19,772 INFO sqlalchemy.engine.Engine [cached since 6.913s ago] ()
INFO:sqlalchemy.engine.Engine:[cached since 6.913s ago] ()
2025-07-15 11:02:19,785 INFO sqlalchemy.engine.Engine 
            SELECT 
                asset_id, fingerprint_hash, asset_type, asset_value,
                asset_host, asset_port, asset_service, confidence,
                status, source, platform_id, project_id,
                discovered_at, verified_at, updated_at, tags, metadata
            FROM unified_assets 
            WHERE 1=1
            ORDER BY updated_at DESC
            LIMIT $1 OFFSET $2
        
INFO:sqlalchemy.engine.Engine:
            SELECT 
                asset_id, fingerprint_hash, asset_type, asset_value,
                asset_host, asset_port, asset_service, confidence,
                status, source, platform_id, project_id,
                discovered_at, verified_at, updated_at, tags, metadata
            FROM unified_assets 
            WHERE 1=1
            ORDER BY updated_at DESC
            LIMIT $1 OFFSET $2
        
2025-07-15 11:02:19,785 INFO sqlalchemy.engine.Engine [cached since 6.92s ago] (5, 0)
INFO:sqlalchemy.engine.Engine:[cached since 6.92s ago] (5, 0)
2025-07-15 11:02:19,788 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:49552 - "GET /api/asset-pipeline/unified-assets?size=5 HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/code/code/bounty-ams/backend/routes/asset_pipeline.py", line 207, in get_unified_assets
    asset = dict(row)
ValueError: dictionary update sequence element #0 has length 41; 2 is required

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
  File "/usr/local/lib/python3.10/dist-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/usr/local/lib/python3.10/dist-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/usr/local/lib/python3.10/dist-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/usr/local/lib/python3.10/dist-packages/starlette/routing.py", line 66, in app
    response = await func(request)
  File "/usr/local/lib/python3.10/dist-packages/fastapi/routing.py", line 274, in app
    raw_response = await run_endpoint_function(
  File "/usr/local/lib/python3.10/dist-packages/fastapi/routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
  File "/home/<USER>/code/code/bounty-ams/backend/routes/asset_pipeline.py", line 236, in get_unified_assets
    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
AttributeError: 'NoneType' object has no attribute 'HTTP_500_INTERNAL_SERVER_ERROR'
2025-07-15 11:02:22,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:22,204 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:22,204 INFO sqlalchemy.engine.Engine [cached since 29.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 29.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:22,209 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:22,210 INFO sqlalchemy.engine.Engine [cached since 29.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 22, 208573), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 29.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 22, 208573), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:22,211 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:22,220 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:22,221 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:22,221 INFO sqlalchemy.engine.Engine [cached since 29.84s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 29.84s ago] ('go-agent-instance-1',)
2025-07-15 11:02:22,225 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:22,225 INFO sqlalchemy.engine.Engine [cached since 29.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 29.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:22,230 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:27,196 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:27,198 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:27,199 INFO sqlalchemy.engine.Engine [cached since 34.84s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 34.84s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:27,202 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:27,202 INFO sqlalchemy.engine.Engine [cached since 34.84s ago] (datetime.datetime(2025, 7, 15, 3, 2, 27, 201712), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 34.84s ago] (datetime.datetime(2025, 7, 15, 3, 2, 27, 201712), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:27,203 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:27,214 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:27,214 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:27,214 INFO sqlalchemy.engine.Engine [cached since 34.83s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 34.83s ago] ('go-agent-instance-1',)
2025-07-15 11:02:27,217 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:27,217 INFO sqlalchemy.engine.Engine [cached since 34.83s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 34.83s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:27,219 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:32,214 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:32,215 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:32,215 INFO sqlalchemy.engine.Engine [cached since 39.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 39.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:32,221 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:32,221 INFO sqlalchemy.engine.Engine [cached since 39.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 32, 220354), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 39.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 32, 220354), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:32,223 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:32,232 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:32,232 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:32,232 INFO sqlalchemy.engine.Engine [cached since 39.85s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 39.85s ago] ('go-agent-instance-1',)
2025-07-15 11:02:32,244 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:32,245 INFO sqlalchemy.engine.Engine [cached since 39.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 39.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:32,248 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:37,208 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:37,210 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:37,210 INFO sqlalchemy.engine.Engine [cached since 44.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 44.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:37,216 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:37,216 INFO sqlalchemy.engine.Engine [cached since 44.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 37, 215382), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 44.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 37, 215382), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:37,218 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:37,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:37,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:37,231 INFO sqlalchemy.engine.Engine [cached since 44.85s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 44.85s ago] ('go-agent-instance-1',)
2025-07-15 11:02:37,234 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:37,234 INFO sqlalchemy.engine.Engine [cached since 44.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 44.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:37,236 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:42,200 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:42,205 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:42,205 INFO sqlalchemy.engine.Engine [cached since 49.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 49.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:42,218 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:42,219 INFO sqlalchemy.engine.Engine [cached since 49.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 42, 217401), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 49.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 42, 217401), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:42,221 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:42,228 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:42,228 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:42,228 INFO sqlalchemy.engine.Engine [cached since 49.85s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 49.85s ago] ('go-agent-instance-1',)
2025-07-15 11:02:42,234 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:42,234 INFO sqlalchemy.engine.Engine [cached since 49.85s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 49.85s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:42,242 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:47,202 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:47,203 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:47,204 INFO sqlalchemy.engine.Engine [cached since 54.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 54.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:47,209 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:47,210 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:47,210 INFO sqlalchemy.engine.Engine [cached since 54.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 54.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:47,216 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:47,216 INFO sqlalchemy.engine.Engine [cached since 54.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 47, 215445), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 54.85s ago] (datetime.datetime(2025, 7, 15, 3, 2, 47, 215445), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:47,217 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:47,217 INFO sqlalchemy.engine.Engine [cached since 54.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 47, 217148), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 54.86s ago] (datetime.datetime(2025, 7, 15, 3, 2, 47, 217148), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:47,218 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:47,226 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:47,226 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:47,226 INFO sqlalchemy.engine.Engine [cached since 54.84s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 54.84s ago] ('go-agent-instance-1',)
2025-07-15 11:02:47,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:47,229 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:47,229 INFO sqlalchemy.engine.Engine [cached since 54.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 54.84s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:48036 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:47,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:47,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:47,231 INFO sqlalchemy.engine.Engine [cached since 54.85s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 54.85s ago] ('go-agent-instance-1',)
2025-07-15 11:02:47,232 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:47,235 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:02:47,235 INFO sqlalchemy.engine.Engine [cached since 29.93s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 2, 47, 233368, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548567}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 29.93s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 2, 47, 233368, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548567}', 'go-agent-instance-1')
2025-07-15 11:02:47,238 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:49546 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:02:52,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:52,210 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:52,210 INFO sqlalchemy.engine.Engine [cached since 59.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 59.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:52,230 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:52,230 INFO sqlalchemy.engine.Engine [cached since 59.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 52, 229214), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 59.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 52, 229214), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:52,232 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:52,244 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:52,244 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:52,244 INFO sqlalchemy.engine.Engine [cached since 59.86s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 59.86s ago] ('go-agent-instance-1',)
2025-07-15 11:02:52,247 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:52,247 INFO sqlalchemy.engine.Engine [cached since 59.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 59.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:52,250 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:02:57,217 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:57,221 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:02:57,221 INFO sqlalchemy.engine.Engine [cached since 64.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 64.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:02:57,230 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:02:57,231 INFO sqlalchemy.engine.Engine [cached since 64.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 57, 229990), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 64.87s ago] (datetime.datetime(2025, 7, 15, 3, 2, 57, 229990), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:02:57,232 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:02:57,237 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:02:57,238 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:02:57,238 INFO sqlalchemy.engine.Engine [cached since 64.86s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 64.86s ago] ('go-agent-instance-1',)
2025-07-15 11:02:57,241 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:02:57,241 INFO sqlalchemy.engine.Engine [cached since 64.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 64.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:02:57,253 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:02,209 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:02,221 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:02,221 INFO sqlalchemy.engine.Engine [cached since 69.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 69.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:02,256 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:02,256 INFO sqlalchemy.engine.Engine [cached since 69.89s ago] (datetime.datetime(2025, 7, 15, 3, 3, 2, 253437), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 69.89s ago] (datetime.datetime(2025, 7, 15, 3, 3, 2, 253437), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:02,257 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:02,268 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:02,268 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:02,268 INFO sqlalchemy.engine.Engine [cached since 69.89s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 69.89s ago] ('go-agent-instance-1',)
2025-07-15 11:03:02,272 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:02,272 INFO sqlalchemy.engine.Engine [cached since 69.89s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 69.89s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:02,273 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:07,201 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:07,202 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:07,202 INFO sqlalchemy.engine.Engine [cached since 74.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 74.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:07,218 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:07,219 INFO sqlalchemy.engine.Engine [cached since 74.86s ago] (datetime.datetime(2025, 7, 15, 3, 3, 7, 218126), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 74.86s ago] (datetime.datetime(2025, 7, 15, 3, 3, 7, 218126), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:07,220 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:07,227 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:07,227 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:07,227 INFO sqlalchemy.engine.Engine [cached since 74.84s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 74.84s ago] ('go-agent-instance-1',)
2025-07-15 11:03:07,231 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:07,231 INFO sqlalchemy.engine.Engine [cached since 74.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 74.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:07,235 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:12,214 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:12,218 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:12,220 INFO sqlalchemy.engine.Engine [cached since 79.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 79.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:12,233 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:12,233 INFO sqlalchemy.engine.Engine [cached since 79.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 12, 231368), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 79.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 12, 231368), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:12,235 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:12,243 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:12,244 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:12,244 INFO sqlalchemy.engine.Engine [cached since 79.86s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 79.86s ago] ('go-agent-instance-1',)
2025-07-15 11:03:12,250 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:12,250 INFO sqlalchemy.engine.Engine [cached since 79.87s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 79.87s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:12,256 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:17,222 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:17,230 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:17,231 INFO sqlalchemy.engine.Engine [cached since 84.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 84.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:17,235 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:17,235 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:17,235 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:17,239 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:17,239 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] (datetime.datetime(2025, 7, 15, 3, 3, 17, 238413), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] (datetime.datetime(2025, 7, 15, 3, 3, 17, 238413), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:17,239 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:17,239 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] (datetime.datetime(2025, 7, 15, 3, 3, 17, 239671), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] (datetime.datetime(2025, 7, 15, 3, 3, 17, 239671), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:17,240 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:17,256 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:17,260 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:17,261 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:17,261 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] ('go-agent-instance-1',)
2025-07-15 11:03:17,262 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:17,262 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:17,262 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] ('go-agent-instance-1',)
2025-07-15 11:03:17,267 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:17,267 INFO sqlalchemy.engine.Engine [cached since 84.88s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 84.88s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
2025-07-15 11:03:17,268 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:03:17,268 INFO sqlalchemy.engine.Engine [cached since 59.97s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 3, 17, 267530, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548597}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 59.97s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 3, 17, 267530, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548597}', 'go-agent-instance-1')
INFO:     127.0.0.1:48720 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:17,270 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:17,272 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:49546 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:03:22,217 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:22,219 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:22,219 INFO sqlalchemy.engine.Engine [cached since 89.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 89.86s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:22,230 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:22,230 INFO sqlalchemy.engine.Engine [cached since 89.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 22, 229258), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 89.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 22, 229258), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:22,232 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:22,242 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:22,243 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:22,243 INFO sqlalchemy.engine.Engine [cached since 89.86s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 89.86s ago] ('go-agent-instance-1',)
2025-07-15 11:03:22,248 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:22,249 INFO sqlalchemy.engine.Engine [cached since 89.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 89.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:22,257 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:27,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:27,206 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:27,206 INFO sqlalchemy.engine.Engine [cached since 94.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 94.85s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:27,217 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:27,217 INFO sqlalchemy.engine.Engine [cached since 94.86s ago] (datetime.datetime(2025, 7, 15, 3, 3, 27, 216243), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 94.86s ago] (datetime.datetime(2025, 7, 15, 3, 3, 27, 216243), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:27,221 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:27,230 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:27,230 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:27,230 INFO sqlalchemy.engine.Engine [cached since 94.85s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 94.85s ago] ('go-agent-instance-1',)
2025-07-15 11:03:27,235 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:27,235 INFO sqlalchemy.engine.Engine [cached since 94.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 94.85s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:27,238 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:32,218 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:32,222 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:32,222 INFO sqlalchemy.engine.Engine [cached since 99.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 99.87s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:32,227 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:32,227 INFO sqlalchemy.engine.Engine [cached since 99.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 32, 226297), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 99.87s ago] (datetime.datetime(2025, 7, 15, 3, 3, 32, 226297), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:32,229 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:32,240 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:32,240 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:32,241 INFO sqlalchemy.engine.Engine [cached since 99.86s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 99.86s ago] ('go-agent-instance-1',)
2025-07-15 11:03:32,244 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:32,244 INFO sqlalchemy.engine.Engine [cached since 99.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 99.86s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:32,250 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:37,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:37,206 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:37,206 INFO sqlalchemy.engine.Engine [cached since 104.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 104.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:37,217 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:37,217 INFO sqlalchemy.engine.Engine [cached since 104.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 37, 216061), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 104.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 37, 216061), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:37,220 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:37,240 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:37,240 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:37,240 INFO sqlalchemy.engine.Engine [cached since 104.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 104.9s ago] ('go-agent-instance-1',)
2025-07-15 11:03:37,244 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:37,244 INFO sqlalchemy.engine.Engine [cached since 104.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 104.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:37,248 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:42,205 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:42,208 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:42,209 INFO sqlalchemy.engine.Engine [cached since 109.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 109.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:42,226 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:42,226 INFO sqlalchemy.engine.Engine [cached since 109.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 42, 226003), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 109.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 42, 226003), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:42,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:42,239 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:42,239 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:42,239 INFO sqlalchemy.engine.Engine [cached since 109.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 109.9s ago] ('go-agent-instance-1',)
2025-07-15 11:03:42,242 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:42,243 INFO sqlalchemy.engine.Engine [cached since 109.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 109.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:42,246 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:47,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:47,213 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:47,214 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:47,219 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:47,219 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:47,219 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:47,227 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:47,227 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 47, 227039), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 47, 227039), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:47,228 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:47,229 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:47,229 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 47, 229093), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 47, 229093), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:47,243 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:47,243 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:47,243 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] ('go-agent-instance-1',)
2025-07-15 11:03:47,244 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:47,245 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:47,245 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:34522 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine [cached since 114.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 114.9s ago] ('go-agent-instance-1',)
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:03:47,247 INFO sqlalchemy.engine.Engine [cached since 89.95s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 3, 47, 247675, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548627}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 89.95s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 3, 47, 247675, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548627}', 'go-agent-instance-1')
2025-07-15 11:03:47,249 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:49546 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:03:52,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:52,209 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:52,209 INFO sqlalchemy.engine.Engine [cached since 119.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 119.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:52,218 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:52,218 INFO sqlalchemy.engine.Engine [cached since 119.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 52, 217322), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 119.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 52, 217322), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:52,220 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:52,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:52,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:52,231 INFO sqlalchemy.engine.Engine [cached since 119.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 119.8s ago] ('go-agent-instance-1',)
2025-07-15 11:03:52,239 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:52,239 INFO sqlalchemy.engine.Engine [cached since 119.9s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 119.9s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=dns_resolution,subdomain_discovery,port_scanning,service_detection,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:52,244 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:03:57,212 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:57,213 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:03:57,214 INFO sqlalchemy.engine.Engine [cached since 124.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 124.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:03:57,232 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:03:57,233 INFO sqlalchemy.engine.Engine [cached since 124.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 57, 231818), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 124.9s ago] (datetime.datetime(2025, 7, 15, 3, 3, 57, 231818), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:03:57,234 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:03:57,246 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:03:57,246 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:03:57,246 INFO sqlalchemy.engine.Engine [cached since 124.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 124.9s ago] ('go-agent-instance-1',)
2025-07-15 11:03:57,249 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:03:57,250 INFO sqlalchemy.engine.Engine [cached since 124.9s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 124.9s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=dns_resolution,subdomain_discovery,port_scanning,service_detection,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:03:57,254 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:02,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:02,213 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:02,213 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 129.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:02,222 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:02,222 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 2, 220530), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 129.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 2, 220530), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:02,223 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:02,237 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:02,237 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:02,237 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 129.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:02,240 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:02,240 INFO sqlalchemy.engine.Engine [cached since 129.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 129.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:02,243 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:07,209 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:07,212 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:07,212 INFO sqlalchemy.engine.Engine [cached since 134.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 134.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:07,221 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:07,221 INFO sqlalchemy.engine.Engine [cached since 134.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 7, 220377), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 134.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 7, 220377), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:07,223 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:07,230 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:07,230 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:07,230 INFO sqlalchemy.engine.Engine [cached since 134.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 134.8s ago] ('go-agent-instance-1',)
2025-07-15 11:04:07,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:07,239 INFO sqlalchemy.engine.Engine [cached since 134.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 134.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:07,242 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:12,221 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:12,223 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:12,223 INFO sqlalchemy.engine.Engine [cached since 139.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 139.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:12,234 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:12,235 INFO sqlalchemy.engine.Engine [cached since 139.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 12, 233233), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 139.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 12, 233233), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:12,238 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:12,268 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:12,268 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:12,268 INFO sqlalchemy.engine.Engine [cached since 139.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 139.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:12,274 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:12,275 INFO sqlalchemy.engine.Engine [cached since 139.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 139.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:12,279 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:17,224 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:17,227 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:17,227 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:17,232 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:17,232 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:17,233 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:17,241 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:17,241 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 17, 240405), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 17, 240405), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:17,242 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:17,243 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 17, 242451), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 17, 242451), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:17,245 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:17,259 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:17,259 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:17,259 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:17,260 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:17,263 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:04:17,263 INFO sqlalchemy.engine.Engine [cached since 120s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 4, 17, 262416, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548657}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 120s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 4, 17, 262416, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548657}', 'go-agent-instance-1')
2025-07-15 11:04:17,265 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:17,265 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:17,265 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:17,265 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:17,267 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:17,268 INFO sqlalchemy.engine.Engine [cached since 144.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 144.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:56392 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
INFO:     127.0.0.1:49546 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:04:17,272 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:22,213 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:22,215 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:22,215 INFO sqlalchemy.engine.Engine [cached since 149.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 149.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:22,228 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:22,228 INFO sqlalchemy.engine.Engine [cached since 149.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 22, 227733), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 149.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 22, 227733), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:22,230 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:22,240 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:22,240 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:22,240 INFO sqlalchemy.engine.Engine [cached since 149.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 149.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:22,245 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:22,245 INFO sqlalchemy.engine.Engine [cached since 149.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 149.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=service_detection,dns_resolution,subdomain_discovery,port_scanning,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:22,250 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:27,200 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:27,203 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:27,204 INFO sqlalchemy.engine.Engine [cached since 154.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 154.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:27,213 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:27,213 INFO sqlalchemy.engine.Engine [cached since 154.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 27, 211999), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 154.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 27, 211999), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:27,214 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:27,223 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:27,223 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:27,224 INFO sqlalchemy.engine.Engine [cached since 154.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 154.8s ago] ('go-agent-instance-1',)
2025-07-15 11:04:27,229 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:27,229 INFO sqlalchemy.engine.Engine [cached since 154.8s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 154.8s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=dns_resolution,subdomain_discovery,port_scanning,service_detection,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:27,232 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:32,218 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:32,223 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:32,223 INFO sqlalchemy.engine.Engine [cached since 159.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 159.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:32,227 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:32,228 INFO sqlalchemy.engine.Engine [cached since 159.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 32, 227203), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 159.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 32, 227203), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:32,228 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:32,235 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:32,235 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:32,235 INFO sqlalchemy.engine.Engine [cached since 159.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 159.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:32,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:32,238 INFO sqlalchemy.engine.Engine [cached since 159.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 159.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:32,249 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:47152 - "GET /api/discovered-assets/stats HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:47176 - "GET /api/tasks?size=10&sort=created_at:desc HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:47164 - "GET /api/tasks/stats HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:47174 - "GET /api/discovered-assets/search?size=10&sort=discovered_at:desc HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:47186 - "GET /api/agents/ HTTP/1.1" 401 Unauthorized
2025-07-15 11:04:37,217 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:37,218 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:37,218 INFO sqlalchemy.engine.Engine [cached since 164.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 164.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:37,235 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:37,235 INFO sqlalchemy.engine.Engine [cached since 164.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 37, 232925), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 164.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 37, 232925), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:37,240 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:37,278 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:37,278 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:37,278 INFO sqlalchemy.engine.Engine [cached since 164.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 164.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:37,284 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:37,285 INFO sqlalchemy.engine.Engine [cached since 164.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 164.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:37,288 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:42,208 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:42,210 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:42,210 INFO sqlalchemy.engine.Engine [cached since 169.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 169.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:42,225 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:42,225 INFO sqlalchemy.engine.Engine [cached since 169.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 42, 224589), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 169.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 42, 224589), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:42,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:42,233 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:42,233 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:42,233 INFO sqlalchemy.engine.Engine [cached since 169.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 169.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:42,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:42,238 INFO sqlalchemy.engine.Engine [cached since 169.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 169.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=service_detection,dns_resolution,subdomain_discovery,port_scanning,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:42,240 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:47,227 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:47,237 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:47,238 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:47,243 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:47,243 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:47,243 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:47,251 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:47,252 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 47, 250064), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 47, 250064), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:47,254 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:47,254 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 47, 253915), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 47, 253915), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:47,255 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:47,264 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:47,264 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:47,265 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:47,265 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:47,268 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:47,268 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:49546 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:47,270 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:47,270 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:47,270 INFO sqlalchemy.engine.Engine [cached since 174.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 174.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:47,270 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:47,272 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:04:47,273 INFO sqlalchemy.engine.Engine [cached since 150s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 4, 47, 271182, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548687}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 150s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 4, 47, 271182, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548687}', 'go-agent-instance-1')
2025-07-15 11:04:47,274 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:44082 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:04:52,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:52,207 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:52,208 INFO sqlalchemy.engine.Engine [cached since 179.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 179.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:52,215 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:52,215 INFO sqlalchemy.engine.Engine [cached since 179.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 52, 214309), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 179.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 52, 214309), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:52,218 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:52,229 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:52,229 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:52,229 INFO sqlalchemy.engine.Engine [cached since 179.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 179.8s ago] ('go-agent-instance-1',)
2025-07-15 11:04:52,233 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:52,233 INFO sqlalchemy.engine.Engine [cached since 179.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 179.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:52,237 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:04:57,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:57,213 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:04:57,213 INFO sqlalchemy.engine.Engine [cached since 184.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 184.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:04:57,226 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:04:57,227 INFO sqlalchemy.engine.Engine [cached since 184.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 57, 226196), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 184.9s ago] (datetime.datetime(2025, 7, 15, 3, 4, 57, 226196), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:04:57,229 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:04:57,240 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:04:57,244 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:04:57,244 INFO sqlalchemy.engine.Engine [cached since 184.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 184.9s ago] ('go-agent-instance-1',)
2025-07-15 11:04:57,246 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:04:57,246 INFO sqlalchemy.engine.Engine [cached since 184.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 184.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:04:57,248 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:02,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:02,209 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:02,209 INFO sqlalchemy.engine.Engine [cached since 189.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 189.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:02,223 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:02,223 INFO sqlalchemy.engine.Engine [cached since 189.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 2, 222897), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 189.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 2, 222897), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:02,224 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:02,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:02,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:02,231 INFO sqlalchemy.engine.Engine [cached since 189.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 189.8s ago] ('go-agent-instance-1',)
2025-07-15 11:05:02,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:02,238 INFO sqlalchemy.engine.Engine [cached since 189.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 189.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:02,240 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:07,203 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:07,207 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:07,207 INFO sqlalchemy.engine.Engine [cached since 194.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 194.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:07,216 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:07,217 INFO sqlalchemy.engine.Engine [cached since 194.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 7, 215567), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 194.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 7, 215567), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:07,218 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:07,229 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:07,229 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:07,229 INFO sqlalchemy.engine.Engine [cached since 194.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 194.8s ago] ('go-agent-instance-1',)
2025-07-15 11:05:07,231 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:07,231 INFO sqlalchemy.engine.Engine [cached since 194.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 194.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:07,233 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:12,205 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:12,216 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:12,217 INFO sqlalchemy.engine.Engine [cached since 199.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 199.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:12,225 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:12,225 INFO sqlalchemy.engine.Engine [cached since 199.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 12, 223975), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 199.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 12, 223975), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:12,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:12,232 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:12,233 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:12,233 INFO sqlalchemy.engine.Engine [cached since 199.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 199.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:12,235 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:12,235 INFO sqlalchemy.engine.Engine [cached since 199.8s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 199.8s ago] ('pending', 1, 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'service_detection', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=dns_resolution,subdomain_discovery,port_scanning,service_detection,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:12,240 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:17,214 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:17,218 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:17,219 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:17,230 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:17,230 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:17,230 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:17,239 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:17,239 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 17, 238709), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 17, 238709), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:17,240 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:17,240 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 17, 240218), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 17, 240218), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:17,241 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:17,246 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:17,246 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:17,246 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:17,247 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:17,253 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:17,253 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:44082 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:17,259 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:17,260 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:17,260 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:17,261 INFO sqlalchemy.engine.Engine [cached since 204.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 204.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:17,261 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:05:17,261 INFO sqlalchemy.engine.Engine [cached since 180s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 5, 17, 261449, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548717}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 180s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 5, 17, 261449, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548717}', 'go-agent-instance-1')
2025-07-15 11:05:17,262 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:47914 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:05:22,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:22,216 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:22,217 INFO sqlalchemy.engine.Engine [cached since 209.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 209.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:22,225 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:22,225 INFO sqlalchemy.engine.Engine [cached since 209.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 22, 224342), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 209.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 22, 224342), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:22,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:22,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:22,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:22,232 INFO sqlalchemy.engine.Engine [cached since 209.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 209.8s ago] ('go-agent-instance-1',)
2025-07-15 11:05:22,238 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:22,238 INFO sqlalchemy.engine.Engine [cached since 209.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 209.9s ago] ('pending', 1, 'service_detection', 'dns_resolution', 'subdomain_discovery', 'port_scanning', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=service_detection,dns_resolution,subdomain_discovery,port_scanning,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:22,246 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:27,208 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:27,210 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:27,215 INFO sqlalchemy.engine.Engine [cached since 214.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 214.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:27,225 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:27,225 INFO sqlalchemy.engine.Engine [cached since 214.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 27, 224127), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 214.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 27, 224127), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:27,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:27,247 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:27,248 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:27,248 INFO sqlalchemy.engine.Engine [cached since 214.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 214.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:27,251 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:27,252 INFO sqlalchemy.engine.Engine [cached since 214.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 214.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:27,257 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:32,210 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:32,212 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:32,212 INFO sqlalchemy.engine.Engine [cached since 219.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 219.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:32,221 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:32,221 INFO sqlalchemy.engine.Engine [cached since 219.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 32, 220711), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 219.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 32, 220711), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:32,222 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:32,237 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:32,237 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:32,237 INFO sqlalchemy.engine.Engine [cached since 219.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 219.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:32,242 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:32,242 INFO sqlalchemy.engine.Engine [cached since 219.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 219.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:32,244 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:37,207 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:37,209 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:37,209 INFO sqlalchemy.engine.Engine [cached since 224.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 224.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:37,226 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:37,226 INFO sqlalchemy.engine.Engine [cached since 224.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 37, 225045), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 224.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 37, 225045), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:37,228 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:37,238 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:37,239 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:37,239 INFO sqlalchemy.engine.Engine [cached since 224.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 224.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:37,244 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:37,244 INFO sqlalchemy.engine.Engine [cached since 224.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 224.9s ago] ('pending', 1, 'port_scanning', 'service_detection', 'dns_resolution', 'subdomain_discovery', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=port_scanning,service_detection,dns_resolution,subdomain_discovery,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:37,247 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51484 - "GET /api/dynamic-models/types HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51488 - "GET /api/dynamic-models/types HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51494 - "GET /api/dynamic-models/all-assets?skip=0&limit=20 HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51510 - "GET /api/discovered-assets/types HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51526 - "GET /api/dynamic-models/all-assets/count HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51532 - "GET /api/discovered-assets/sources HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51534 - "GET /api/dynamic-models/all-assets?skip=0&limit=20 HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51536 - "GET /api/discovered-assets/types HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51540 - "GET /api/dynamic-models/all-assets/count HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51548 - "GET /api/discovered-assets/sources HTTP/1.1" 401 Unauthorized
2025-07-15 11:05:42,199 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:42,206 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:42,206 INFO sqlalchemy.engine.Engine [cached since 229.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 229.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:42,229 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:42,229 INFO sqlalchemy.engine.Engine [cached since 229.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 42, 214099), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 229.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 42, 214099), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:42,231 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:42,233 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:42,233 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:42,233 INFO sqlalchemy.engine.Engine [cached since 229.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 229.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:42,235 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:42,235 INFO sqlalchemy.engine.Engine [cached since 229.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 229.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:42,239 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:47,218 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:47,222 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:47,223 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:47,233 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:47,234 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:47,234 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:47,240 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:47,240 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 47, 239331), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 47, 239331), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:47,241 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:47,242 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 47, 241535), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 47, 241535), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:47,243 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:47,258 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:47,258 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:47,258 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:47,259 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:47,261 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:47,262 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:47914 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:47,266 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:47,266 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:47,266 INFO sqlalchemy.engine.Engine [cached since 234.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 234.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:47,267 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:47,269 INFO sqlalchemy.engine.Engine UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
INFO:sqlalchemy.engine.Engine:UPDATE agents SET current_tasks=$1::INTEGER, status=$2::VARCHAR, last_seen_at=$3::TIMESTAMP WITH TIME ZONE, agent_metadata=$4::JSON, updated_at=now() WHERE agents.agent_id = $5::VARCHAR
2025-07-15 11:05:47,269 INFO sqlalchemy.engine.Engine [cached since 210s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 5, 47, 268960, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548747}', 'go-agent-instance-1')
INFO:sqlalchemy.engine.Engine:[cached since 210s ago] (0, 'online', datetime.datetime(2025, 7, 15, 3, 5, 47, 268960, tzinfo=datetime.timezone.utc), '{"cpu_usage": 25.5, "memory_usage": 512, "uptime": 1752548747}', 'go-agent-instance-1')
2025-07-15 11:05:47,271 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
INFO:     127.0.0.1:36428 - "POST /api/agents/heartbeat-with-key HTTP/1.1" 200 OK
2025-07-15 11:05:48,140 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:48,142 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:48,142 INFO sqlalchemy.engine.Engine [generated in 0.00014s] ('admin',)
INFO:sqlalchemy.engine.Engine:[generated in 0.00014s] ('admin',)
WARNING:passlib.handlers.bcrypt:(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/usr/local/lib/python3.10/dist-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     127.0.0.1:36442 - "POST /api/auth/login HTTP/1.1" 200 OK
2025-07-15 11:05:48,442 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:48,503 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:48,503 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:48,503 INFO sqlalchemy.engine.Engine [cached since 0.3615s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 0.3615s ago] ('admin',)
INFO:     127.0.0.1:36444 - "GET /api/auth/me HTTP/1.1" 200 OK
2025-07-15 11:05:48,522 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:52,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:52,208 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:52,209 INFO sqlalchemy.engine.Engine [cached since 239.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 239.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:52,224 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:52,224 INFO sqlalchemy.engine.Engine [cached since 239.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 52, 221976), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 239.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 52, 221976), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:52,227 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:52,236 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:52,236 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:52,236 INFO sqlalchemy.engine.Engine [cached since 239.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 239.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:52,241 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:52,242 INFO sqlalchemy.engine.Engine [cached since 239.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 239.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:36428 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:52,246 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:57,206 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,211 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:05:57,211 INFO sqlalchemy.engine.Engine [cached since 244.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 244.9s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:05:57,226 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:05:57,226 INFO sqlalchemy.engine.Engine [cached since 244.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 57, 223409), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 244.9s ago] (datetime.datetime(2025, 7, 15, 3, 5, 57, 223409), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:05:57,228 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:05:57,247 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,247 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:05:57,247 INFO sqlalchemy.engine.Engine [cached since 244.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 244.9s ago] ('go-agent-instance-1',)
2025-07-15 11:05:57,251 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:05:57,252 INFO sqlalchemy.engine.Engine [cached since 244.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 244.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:36428 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:05:57,254 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:57,483 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,483 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:57,483 INFO sqlalchemy.engine.Engine [cached since 9.342s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 9.342s ago] ('admin',)
2025-07-15 11:05:57,491 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,491 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:57,491 INFO sqlalchemy.engine.Engine [cached since 9.35s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 9.35s ago] ('admin',)
2025-07-15 11:05:57,552 INFO sqlalchemy.engine.Engine SELECT tasks.status, count(tasks.id) AS count 
FROM tasks GROUP BY tasks.status
INFO:sqlalchemy.engine.Engine:SELECT tasks.status, count(tasks.id) AS count 
FROM tasks GROUP BY tasks.status
2025-07-15 11:05:57,552 INFO sqlalchemy.engine.Engine [generated in 0.00034s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00034s] ()
INFO:     127.0.0.1:51410 - "GET /api/tasks/stats HTTP/1.1" 200 OK
2025-07-15 11:05:57,637 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:05:57,650 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,651 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:57,651 INFO sqlalchemy.engine.Engine [cached since 9.509s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 9.509s ago] ('admin',)
2025-07-15 11:05:57,652 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:05:57,652 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:05:57,652 INFO sqlalchemy.engine.Engine [cached since 9.511s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 9.511s ago] ('admin',)
2025-07-15 11:05:57,656 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
2025-07-15 11:05:57,656 INFO sqlalchemy.engine.Engine [generated in 0.00028s] ()
INFO:sqlalchemy.engine.Engine:[generated in 0.00028s] ()
INFO:     127.0.0.1:51434 - "GET /api/agents/ HTTP/1.1" 200 OK
2025-07-15 11:05:57,661 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:elastic_transport.transport:POST http://localhost:9200/enhanced_asset-*/_search [status:200 duration:0.125s]
INFO:     127.0.0.1:51394 - "GET /api/discovered-assets/stats HTTP/1.1" 200 OK
2025-07-15 11:05:57,685 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:elastic_transport.transport:POST http://localhost:9200/enhanced_asset-*/_search [status:200 duration:0.037s]
INFO:     127.0.0.1:51422 - "GET /api/discovered-assets/search?size=10&sort=discovered_at:desc HTTP/1.1" 200 OK
2025-07-15 11:05:57,694 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:51446 - "GET /api/tasks?size=10&sort=created_at:desc HTTP/1.1" 307 Temporary Redirect
2025-07-15 11:06:02,201 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:02,202 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:06:02,202 INFO sqlalchemy.engine.Engine [cached since 249.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 249.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:06:02,211 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:06:02,211 INFO sqlalchemy.engine.Engine [cached since 249.8s ago] (datetime.datetime(2025, 7, 15, 3, 6, 2, 210394), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 249.8s ago] (datetime.datetime(2025, 7, 15, 3, 6, 2, 210394), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:06:02,213 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:06:02,230 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:02,231 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:06:02,231 INFO sqlalchemy.engine.Engine [cached since 249.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 249.8s ago] ('go-agent-instance-1',)
2025-07-15 11:06:02,241 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:06:02,241 INFO sqlalchemy.engine.Engine [cached since 249.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 249.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:36428 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:06:02,257 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:07,112 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:07,114 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:06:07,114 INFO sqlalchemy.engine.Engine [cached since 18.97s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 18.97s ago] ('admin',)
2025-07-15 11:06:07,120 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:07,120 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:06:07,121 INFO sqlalchemy.engine.Engine [cached since 18.98s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 18.98s ago] ('admin',)
2025-07-15 11:06:07,132 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
2025-07-15 11:06:07,132 INFO sqlalchemy.engine.Engine [cached since 9.476s ago] ()
INFO:sqlalchemy.engine.Engine:[cached since 9.476s ago] ()
INFO:     127.0.0.1:51720 - "GET /api/agents/ HTTP/1.1" 200 OK
2025-07-15 11:06:07,143 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:elastic_transport.transport:POST http://localhost:9200/enhanced_asset-*/_search [status:200 duration:0.047s]
INFO:     127.0.0.1:51708 - "GET /api/discovered-assets/stats HTTP/1.1" 200 OK
2025-07-15 11:06:07,178 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:07,192 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:07,192 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:06:07,192 INFO sqlalchemy.engine.Engine [cached since 254.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 254.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:06:07,196 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:06:07,196 INFO sqlalchemy.engine.Engine [cached since 254.8s ago] (datetime.datetime(2025, 7, 15, 3, 6, 7, 194780), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 254.8s ago] (datetime.datetime(2025, 7, 15, 3, 6, 7, 194780), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:06:07,201 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:06:07,209 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:07,209 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:06:07,209 INFO sqlalchemy.engine.Engine [cached since 254.8s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 254.8s ago] ('go-agent-instance-1',)
2025-07-15 11:06:07,212 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:06:07,212 INFO sqlalchemy.engine.Engine [cached since 254.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 254.8s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:36428 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:06:07,213 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:08,730 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:08,732 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:06:08,732 INFO sqlalchemy.engine.Engine [cached since 20.59s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 20.59s ago] ('admin',)
2025-07-15 11:06:08,743 INFO sqlalchemy.engine.Engine SELECT tasks.status, count(tasks.id) AS count 
FROM tasks GROUP BY tasks.status
INFO:sqlalchemy.engine.Engine:SELECT tasks.status, count(tasks.id) AS count 
FROM tasks GROUP BY tasks.status
2025-07-15 11:06:08,743 INFO sqlalchemy.engine.Engine [cached since 11.19s ago] ()
INFO:sqlalchemy.engine.Engine:[cached since 11.19s ago] ()
INFO:     127.0.0.1:51736 - "GET /api/tasks/stats HTTP/1.1" 200 OK
2025-07-15 11:06:08,751 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:09,101 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:09,101 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:06:09,102 INFO sqlalchemy.engine.Engine [cached since 20.96s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 20.96s ago] ('admin',)
2025-07-15 11:06:09,111 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents ORDER BY agents.created_at DESC
2025-07-15 11:06:09,111 INFO sqlalchemy.engine.Engine [cached since 11.46s ago] ()
INFO:sqlalchemy.engine.Engine:[cached since 11.46s ago] ()
INFO:     127.0.0.1:51738 - "GET /api/agents/ HTTP/1.1" 200 OK
2025-07-15 11:06:09,112 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:09,122 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:09,122 INFO sqlalchemy.engine.Engine SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT users.id, users.username, users.email, users.hashed_password, users.is_admin, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = $1::VARCHAR
2025-07-15 11:06:09,122 INFO sqlalchemy.engine.Engine [cached since 20.98s ago] ('admin',)
INFO:sqlalchemy.engine.Engine:[cached since 20.98s ago] ('admin',)
INFO:elastic_transport.transport:POST http://localhost:9200/enhanced_asset-*/_search [status:200 duration:0.032s]
INFO:     127.0.0.1:51746 - "GET /api/discovered-assets/stats HTTP/1.1" 200 OK
2025-07-15 11:06:09,159 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-15 11:06:12,199 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:12,201 INFO sqlalchemy.engine.Engine SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agent_keys.id, agent_keys.key_id, agent_keys.key_hash, agent_keys.agent_id, agent_keys.name, agent_keys.description, agent_keys.status, agent_keys.expires_at, agent_keys.created_by_user_id, agent_keys.created_at, agent_keys.updated_at, agent_keys.last_used_at, agent_keys.usage_count 
FROM agent_keys 
WHERE agent_keys.key_hash = $1::VARCHAR AND agent_keys.status = $2::VARCHAR
2025-07-15 11:06:12,202 INFO sqlalchemy.engine.Engine [cached since 259.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
INFO:sqlalchemy.engine.Engine:[cached since 259.8s ago] ('f3cb5adf4574c7fbc0422b08256aa26fc5d3d6d93a2a68b6a2ad41a64353d8d9', 'active')
2025-07-15 11:06:12,227 INFO sqlalchemy.engine.Engine UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
INFO:sqlalchemy.engine.Engine:UPDATE agent_keys SET updated_at=now(), last_used_at=$1::TIMESTAMP WITH TIME ZONE, usage_count=(agent_keys.usage_count + $2::INTEGER) WHERE agent_keys.id = $3::UUID
2025-07-15 11:06:12,227 INFO sqlalchemy.engine.Engine [cached since 259.9s ago] (datetime.datetime(2025, 7, 15, 3, 6, 12, 223766), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
INFO:sqlalchemy.engine.Engine:[cached since 259.9s ago] (datetime.datetime(2025, 7, 15, 3, 6, 12, 223766), 1, UUID('d0199190-8d55-49d7-842d-1cb19c666ca3'))
2025-07-15 11:06:12,233 INFO sqlalchemy.engine.Engine COMMIT
INFO:sqlalchemy.engine.Engine:COMMIT
2025-07-15 11:06:12,239 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-15 11:06:12,240 INFO sqlalchemy.engine.Engine SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
INFO:sqlalchemy.engine.Engine:SELECT agents.id, agents.agent_id, agents.name, agents.version, agents.capabilities, agents.max_concurrent_tasks, agents.current_tasks, agents.status, agents.hostname, agents.ip_address, agents.last_seen_at, agents.agent_metadata, agents.created_at, agents.updated_at 
FROM agents 
WHERE agents.agent_id = $1::VARCHAR
2025-07-15 11:06:12,240 INFO sqlalchemy.engine.Engine [cached since 259.9s ago] ('go-agent-instance-1',)
INFO:sqlalchemy.engine.Engine:[cached since 259.9s ago] ('go-agent-instance-1',)
2025-07-15 11:06:12,245 INFO sqlalchemy.engine.Engine SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
INFO:sqlalchemy.engine.Engine:SELECT tasks.id, tasks.task_id, tasks.task_type, tasks.target, tasks.parameters, tasks.priority, tasks.status, tasks.agent_id, tasks.preferred_agent_id, tasks.workflow_id, tasks.timeout, tasks.retry_count, tasks.current_retry, tasks.dependencies, tasks.result_data, tasks.error_message, tasks.execution_time, tasks.assets_discovered, tasks.created_at, tasks.assigned_at, tasks.started_at, tasks.completed_at, tasks.created_by_user_id 
FROM tasks 
WHERE tasks.status = $1::VARCHAR AND tasks.task_type IN ($3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::VARCHAR, $7::VARCHAR, $8::VARCHAR, $9::VARCHAR, $10::VARCHAR, $11::VARCHAR) ORDER BY tasks.priority DESC, tasks.created_at ASC 
 LIMIT $2::INTEGER
2025-07-15 11:06:12,245 INFO sqlalchemy.engine.Engine [cached since 259.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:sqlalchemy.engine.Engine:[cached since 259.9s ago] ('pending', 1, 'subdomain_discovery', 'port_scanning', 'service_detection', 'dns_resolution', 'agent_pause', 'agent_resume', 'agent_stop', 'agent_restart', 'agent_cancel_tasks')
INFO:     127.0.0.1:36428 - "GET /api/agents/tasks/poll-with-key?api_key=bams_agent_ak_yez6epu5gnbau6w3soa1khpy1jay2czt&agent_id=go-agent-instance-1&capabilities=subdomain_discovery,port_scanning,service_detection,dns_resolution,agent_pause,agent_resume,agent_stop,agent_restart,agent_cancel_tasks HTTP/1.1" 200 OK
2025-07-15 11:06:12,254 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
